# CMDB项目DDD架构重构文档

## 概述

本文档描述了CMDB项目从传统分层架构重构为领域驱动设计(DDD)架构的详细方案。重构以资产管理域为示例，展示了完整的DDD实现。

## 架构设计

### 四层架构

```
internal/
├── domain/           # 领域层 - 核心业务逻辑
│   ├── asset/       # 资产管理领域
│   │   ├── entity/          # 实体
│   │   ├── valueobject/     # 值对象
│   │   ├── repository/      # 仓储接口
│   │   └── service/         # 领域服务
│   ├── monitoring/  # 监控领域
│   └── ...
├── application/     # 应用层 - 应用服务协调
│   └── asset/       # 资产应用服务
├── infrastructure/ # 基础设施层 - 技术实现
│   └── persistence/ # 数据持久化
└── interfaces/     # 用户界面层 - API接口
    └── http/        # HTTP接口
```

### 领域划分

1. **资产管理域(Asset Management)**：主机、云账户、数据中心、业务
2. **监控域(Monitoring)**：告警、指标、健康检查
3. **统计分析域(Analytics)**：各类统计报表和数据分析
4. **用户认证域(Authentication)**：用户、权限、审计
5. **应用运维域(Application Operations)**：域名、代码、部署
6. **数据库管理域(Database Management)**：MySQL、Redis、MongoDB
7. **任务调度域(Task Scheduling)**：批量任务、定时任务
8. **工作流域(Workflow)**：审批流程、订单管理

## 资产管理域实现

### 1. 领域层(Domain Layer)

#### 实体(Entity)

**主机聚合根 - Host**
```go
type Host struct {
    id           HostID
    name         string
    ipAddress    valueobject.IPAddress
    cloudAccount *CloudAccount
    datacenter   *Datacenter
    business     *Business
    tags         []valueobject.Tag
    status       HostStatus
    // ...
}
```

**云账户实体 - CloudAccount**
```go
type CloudAccount struct {
    id          CloudAccountID
    name        string
    provider    CloudProvider
    accessKey   string
    secretKey   string
    // ...
}
```

#### 值对象(Value Object)

**IP地址值对象**
```go
type IPAddress struct {
    value string
    ip    net.IP
}
```

**标签值对象**
```go
type Tag struct {
    key   string
    value string
}
```

#### 仓储接口(Repository Interface)

```go
type HostRepository interface {
    Save(ctx context.Context, host *Host) error
    FindByID(ctx context.Context, id HostID) (*Host, error)
    FindAll(ctx context.Context, criteria HostSearchCriteria) ([]*Host, error)
    // ...
}
```

#### 领域服务(Domain Service)

```go
type HostDomainService struct {
    hostRepo         HostRepository
    cloudAccountRepo CloudAccountRepository
    // ...
}

func (s *HostDomainService) CreateHost(ctx context.Context, req CreateHostRequest) (*Host, error)
func (s *HostDomainService) ValidateHostNetworkConflict(ctx context.Context, host *Host, mask net.IPMask) ([]*Host, error)
```

### 2. 应用层(Application Layer)

#### 应用服务(Application Service)

```go
type HostApplicationService struct {
    hostRepo        HostRepository
    hostDomainSvc   *HostDomainService
    eventPublisher  EventPublisher
    transactionMgr  TransactionManager
}

func (s *HostApplicationService) CreateHost(ctx context.Context, cmd CreateHostCommand) (*HostDTO, error)
func (s *HostApplicationService) UpdateHost(ctx context.Context, hostID HostID, cmd UpdateHostCommand) (*HostDTO, error)
```

#### 数据传输对象(DTO)

```go
type HostDTO struct {
    ID           uint64            `json:"id"`
    Name         string            `json:"name"`
    IPAddress    string            `json:"ip_address"`
    Status       string            `json:"status"`
    CloudAccount *CloudAccountDTO  `json:"cloud_account,omitempty"`
    Tags         []TagDTO          `json:"tags"`
    // ...
}
```

#### 命令和查询(Command & Query)

```go
type CreateHostCommand struct {
    Name           string                     `json:"name"`
    IPAddress      string                     `json:"ip_address"`
    CloudAccountID *CloudAccountID           `json:"cloud_account_id,omitempty"`
    Tags           []TagDTO                   `json:"tags,omitempty"`
}

type ListHostsQuery struct {
    Name           string         `json:"name,omitempty"`
    IPAddress      string         `json:"ip_address,omitempty"`
    Status         *HostStatus    `json:"status,omitempty"`
    Offset         int            `json:"offset,omitempty"`
    Limit          int            `json:"limit,omitempty"`
    // ...
}
```

#### 领域事件(Domain Event)

```go
type HostCreatedEvent struct {
    HostID    HostID    `json:"host_id"`
    Name      string    `json:"name"`
    IPAddress string    `json:"ip_address"`
    CreatedAt time.Time `json:"created_at"`
}
```

### 3. 基础设施层(Infrastructure Layer)

#### 仓储实现(Repository Implementation)

```go
type HostRepositoryImpl struct {
    db *gorm.DB
}

func (r *HostRepositoryImpl) Save(ctx context.Context, host *Host) error {
    po := r.entityToPO(host)
    return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
        // 保存逻辑
    })
}
```

#### 持久化对象(Persistence Object)

```go
type HostPO struct {
    ID             uint64 `gorm:"column:id;primaryKey;autoIncrement"`
    Name           string `gorm:"column:name;type:varchar(255);not null;uniqueIndex"`
    IPAddress      string `gorm:"column:ip_address;type:varchar(45);not null;uniqueIndex"`
    CloudAccountID *uint64 `gorm:"column:cloud_account_id;index"`
    Status         int8    `gorm:"column:status;not null;default:1"`
    // ...
}
```

### 4. 用户界面层(Interface Layer)

#### HTTP处理器(HTTP Handler)

```go
type HostHandler struct {
    hostAppSvc *HostApplicationService
}

func (h *HostHandler) CreateHost(c *gin.Context) {
    var cmd CreateHostCommand
    if err := c.ShouldBindJSON(&cmd); err != nil {
        response.FailWithMessage(c, "请求参数错误: "+err.Error())
        return
    }
    
    hostDTO, err := h.hostAppSvc.CreateHost(c.Request.Context(), cmd)
    if err != nil {
        response.FailWithMessage(c, "创建主机失败: "+err.Error())
        return
    }
    
    response.OkWithData(c, hostDTO)
}
```

## 核心概念实现

### 1. 聚合根(Aggregate Root)

- **Host** 作为主机聚合的根实体
- 封装业务规则和不变性约束
- 控制对聚合内部对象的访问

### 2. 值对象(Value Object)

- **IPAddress**: 封装IP地址验证和操作
- **Tag**: 封装标签的键值对和验证规则
- 不可变性保证数据一致性

### 3. 领域服务(Domain Service)

- **HostDomainService**: 处理跨实体的业务逻辑
- 网络冲突检测、批量操作等复杂业务规则

### 4. 仓储模式(Repository Pattern)

- 抽象数据访问逻辑
- 支持不同的持久化实现
- 提供丰富的查询接口

### 5. 领域事件(Domain Event)

- 解耦聚合间的依赖关系
- 支持最终一致性
- 便于集成和扩展

## 技术特性

### 1. 类型安全

- 强类型ID避免混淆
- 值对象保证数据有效性
- 编译时错误检查

### 2. 业务规则封装

- 实体内部封装业务逻辑
- 领域服务处理复杂规则
- 防止贫血模型

### 3. 测试友好

- 依赖注入便于单元测试
- 接口抽象便于Mock
- 纯函数易于测试

### 4. 扩展性

- 清晰的层次边界
- 插件化的基础设施
- 事件驱动的集成

## 迁移策略

### 1. 渐进式重构

- 先重构核心领域（资产管理）
- 保持现有API兼容性
- 逐步迁移其他领域

### 2. 数据库兼容

- 保持现有表结构
- 通过PO对象适配
- 渐进式数据迁移

### 3. API兼容

- 保持现有接口地址
- DTO转换保证数据格式
- 向后兼容性保证

## 下一步计划

1. **完善资产管理域**
   - 实现云账户、数据中心、业务的完整DDD模型
   - 添加更多业务规则和验证

2. **扩展到其他领域**
   - 监控域的告警聚合
   - 统计分析域的报表模型
   - 用户认证域的权限模型

3. **基础设施完善**
   - 事件发布器实现
   - 事务管理器实现
   - 缓存和性能优化

4. **测试和文档**
   - 单元测试覆盖
   - 集成测试
   - API文档更新

## 总结

通过DDD架构重构，CMDB项目获得了：

- **更清晰的业务模型**：领域概念直接映射到代码
- **更好的可维护性**：职责分离，依赖清晰
- **更强的扩展性**：插件化架构，事件驱动
- **更高的代码质量**：类型安全，业务规则封装

这为项目的长期发展奠定了坚实的架构基础。
