package response

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// Response 统一响应结构
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// 响应状态码
const (
	SUCCESS = 200
	ERROR   = 500
)

// Ok 成功响应
func Ok(c *gin.Context) {
	c.JSON(http.StatusOK, Response{
		Code:    SUCCESS,
		Message: "操作成功",
	})
}

// OkWithMessage 成功响应带消息
func OkWithMessage(c *gin.Context, message string) {
	c.JSON(http.StatusOK, Response{
		Code:    SUCCESS,
		Message: message,
	})
}

// OkWithData 成功响应带数据
func OkWithData(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, Response{
		Code:    SUCCESS,
		Message: "操作成功",
		Data:    data,
	})
}

// OkWithDetailed 成功响应带详细信息
func OkWithDetailed(c *gin.Context, data interface{}, message string) {
	c.<PERSON><PERSON><PERSON>(http.StatusOK, Response{
		Code:    SUCCESS,
		Message: message,
		Data:    data,
	})
}

// Fail 失败响应
func Fail(c *gin.Context) {
	c.JSON(http.StatusOK, Response{
		Code:    ERROR,
		Message: "操作失败",
	})
}

// FailWithMessage 失败响应带消息
func FailWithMessage(c *gin.Context, message string) {
	c.JSON(http.StatusOK, Response{
		Code:    ERROR,
		Message: message,
	})
}

// FailWithData 失败响应带数据
func FailWithData(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, Response{
		Code:    ERROR,
		Message: "操作失败",
		Data:    data,
	})
}

// FailWithDetailed 失败响应带详细信息
func FailWithDetailed(c *gin.Context, data interface{}, message string) {
	c.JSON(http.StatusOK, Response{
		Code:    ERROR,
		Message: message,
		Data:    data,
	})
}

// FailWithCode 失败响应带状态码
func FailWithCode(c *gin.Context, code int, message string) {
	c.JSON(http.StatusOK, Response{
		Code:    code,
		Message: message,
	})
}

// BadRequest 400错误
func BadRequest(c *gin.Context, message string) {
	c.JSON(http.StatusBadRequest, Response{
		Code:    http.StatusBadRequest,
		Message: message,
	})
}

// Unauthorized 401错误
func Unauthorized(c *gin.Context, message string) {
	c.JSON(http.StatusUnauthorized, Response{
		Code:    http.StatusUnauthorized,
		Message: message,
	})
}

// Forbidden 403错误
func Forbidden(c *gin.Context, message string) {
	c.JSON(http.StatusForbidden, Response{
		Code:    http.StatusForbidden,
		Message: message,
	})
}

// NotFound 404错误
func NotFound(c *gin.Context, message string) {
	c.JSON(http.StatusNotFound, Response{
		Code:    http.StatusNotFound,
		Message: message,
	})
}

// InternalServerError 500错误
func InternalServerError(c *gin.Context, message string) {
	c.JSON(http.StatusInternalServerError, Response{
		Code:    http.StatusInternalServerError,
		Message: message,
	})
}
