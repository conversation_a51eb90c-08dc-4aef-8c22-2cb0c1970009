package api

import (
	"cmdb/app/api/appops"
	"cmdb/app/api/appops/domain"
	"cmdb/app/api/appops/gitcode"
	"cmdb/app/api/appops/nginx"

	"github.com/gin-gonic/gin"
)

// setupAppOpsRoutes 设置应用运维相关路由
func setupAppOpsRoutes(apiV1GR *gin.RouterGroup) {
	// 应用运维路由（需要管理员权限）
	appOpsGRAdmin := apiV1GR.Group("/appops", adminRequired())
	{
		// 应用管理
		appOpsGRAdmin.GET("/apps", appops.GetApps)
		appOpsGRAdmin.POST("/apps", appops.CreateApp)
		appOpsGRAdmin.PUT("/apps/:id", appops.UpdateApp)
		appOpsGRAdmin.DELETE("/apps/:id", appops.DeleteApp)
		appOpsGRAdmin.GET("/apps/:id", appops.GetApp)
		appOpsGRAdmin.GET("/all/apps", appops.GetAllApps)

		// 应用部署
		appOpsGRAdmin.GET("/apps/:id/deployments", appops.GetAppDeployments)
		appOpsGRAdmin.POST("/apps/:id/deployments", appops.CreateAppDeployment)
		appOpsGRAdmin.GET("/deployments/:id", appops.GetDeployment)
		appOpsGRAdmin.PUT("/deployments/:id", appops.UpdateDeployment)
		appOpsGRAdmin.DELETE("/deployments/:id", appops.DeleteDeployment)

		// 应用配置
		appOpsGRAdmin.GET("/apps/:id/configs", appops.GetAppConfigs)
		appOpsGRAdmin.POST("/apps/:id/configs", appops.CreateAppConfig)
		appOpsGRAdmin.PUT("/configs/:id", appops.UpdateAppConfig)
		appOpsGRAdmin.DELETE("/configs/:id", appops.DeleteAppConfig)

		// 应用监控
		appOpsGRAdmin.GET("/apps/:id/monitors", appops.GetAppMonitors)
		appOpsGRAdmin.POST("/apps/:id/monitors", appops.CreateAppMonitor)
		appOpsGRAdmin.PUT("/monitors/:id", appops.UpdateAppMonitor)
		appOpsGRAdmin.DELETE("/monitors/:id", appops.DeleteAppMonitor)

		// 域名管理
		setupDomainRoutes(appOpsGRAdmin)

		// Git代码管理
		setupGitCodeRoutes(appOpsGRAdmin)

		// Nginx配置管理
		setupNginxRoutes(appOpsGRAdmin)
	}
}

// setupDomainRoutes 设置域名管理路由
func setupDomainRoutes(appOpsGRAdmin *gin.RouterGroup) {
	domainGR := appOpsGRAdmin.Group("/domains")
	{
		// 域名基础管理
		domainGR.GET("", domain.GetDomains)
		domainGR.POST("", domain.CreateDomain)
		domainGR.PUT("/:id", domain.UpdateDomain)
		domainGR.DELETE("/:id", domain.DeleteDomain)
		domainGR.GET("/:id", domain.GetDomain)
		domainGR.GET("/all", domain.GetAllDomains)

		// 域名解析记录
		domainGR.GET("/:id/records", domain.GetDomainRecords)
		domainGR.POST("/:id/records", domain.CreateDomainRecord)
		domainGR.PUT("/records/:record_id", domain.UpdateDomainRecord)
		domainGR.DELETE("/records/:record_id", domain.DeleteDomainRecord)

		// 域名证书管理
		domainGR.GET("/:id/certificates", domain.GetDomainCertificates)
		domainGR.POST("/:id/certificates", domain.CreateDomainCertificate)
		domainGR.PUT("/certificates/:cert_id", domain.UpdateDomainCertificate)
		domainGR.DELETE("/certificates/:cert_id", domain.DeleteDomainCertificate)
		domainGR.POST("/certificates/:cert_id/renew", domain.RenewDomainCertificate)

		// 域名监控
		domainGR.GET("/:id/monitors", domain.GetDomainMonitors)
		domainGR.POST("/:id/monitors", domain.CreateDomainMonitor)
		domainGR.PUT("/monitors/:monitor_id", domain.UpdateDomainMonitor)
		domainGR.DELETE("/monitors/:monitor_id", domain.DeleteDomainMonitor)

		// 域名统计
		domainGR.GET("/:id/statistics", domain.GetDomainStatistics)
		domainGR.GET("/statistics/summary", domain.GetDomainsStatisticsSummary)
	}
}

// setupGitCodeRoutes 设置Git代码管理路由
func setupGitCodeRoutes(appOpsGRAdmin *gin.RouterGroup) {
	gitCodeGR := appOpsGRAdmin.Group("/gitcode")
	{
		// 代码仓库管理
		gitCodeGR.GET("/repositories", gitcode.GetRepositories)
		gitCodeGR.POST("/repositories", gitcode.CreateRepository)
		gitCodeGR.PUT("/repositories/:id", gitcode.UpdateRepository)
		gitCodeGR.DELETE("/repositories/:id", gitcode.DeleteRepository)
		gitCodeGR.GET("/repositories/:id", gitcode.GetRepository)
		gitCodeGR.GET("/all/repositories", gitcode.GetAllRepositories)

		// 分支管理
		gitCodeGR.GET("/repositories/:id/branches", gitcode.GetRepositoryBranches)
		gitCodeGR.POST("/repositories/:id/branches", gitcode.CreateRepositoryBranch)
		gitCodeGR.DELETE("/repositories/:id/branches/:branch", gitcode.DeleteRepositoryBranch)

		// 标签管理
		gitCodeGR.GET("/repositories/:id/tags", gitcode.GetRepositoryTags)
		gitCodeGR.POST("/repositories/:id/tags", gitcode.CreateRepositoryTag)
		gitCodeGR.DELETE("/repositories/:id/tags/:tag", gitcode.DeleteRepositoryTag)

		// 提交记录
		gitCodeGR.GET("/repositories/:id/commits", gitcode.GetRepositoryCommits)
		gitCodeGR.GET("/repositories/:id/commits/:sha", gitcode.GetRepositoryCommit)

		// 代码统计
		gitCodeGR.GET("/repositories/:id/statistics", gitcode.GetRepositoryStatistics)
		gitCodeGR.GET("/statistics/summary", gitcode.GetRepositoriesStatisticsSummary)

		// Webhook管理
		gitCodeGR.GET("/repositories/:id/webhooks", gitcode.GetRepositoryWebhooks)
		gitCodeGR.POST("/repositories/:id/webhooks", gitcode.CreateRepositoryWebhook)
		gitCodeGR.PUT("/webhooks/:webhook_id", gitcode.UpdateRepositoryWebhook)
		gitCodeGR.DELETE("/webhooks/:webhook_id", gitcode.DeleteRepositoryWebhook)
	}
}

// setupNginxRoutes 设置Nginx配置管理路由
func setupNginxRoutes(appOpsGRAdmin *gin.RouterGroup) {
	nginxGR := appOpsGRAdmin.Group("/nginx")
	{
		// Nginx服务器管理
		nginxGR.GET("/servers", nginx.GetNginxServers)
		nginxGR.POST("/servers", nginx.CreateNginxServer)
		nginxGR.PUT("/servers/:id", nginx.UpdateNginxServer)
		nginxGR.DELETE("/servers/:id", nginx.DeleteNginxServer)
		nginxGR.GET("/servers/:id", nginx.GetNginxServer)
		nginxGR.GET("/all/servers", nginx.GetAllNginxServers)

		// Nginx配置管理
		nginxGR.GET("/servers/:id/configs", nginx.GetNginxServerConfigs)
		nginxGR.POST("/servers/:id/configs", nginx.CreateNginxServerConfig)
		nginxGR.PUT("/configs/:config_id", nginx.UpdateNginxServerConfig)
		nginxGR.DELETE("/configs/:config_id", nginx.DeleteNginxServerConfig)
		nginxGR.GET("/configs/:config_id", nginx.GetNginxServerConfig)

		// Nginx配置模板
		nginxGR.GET("/templates", nginx.GetNginxConfigTemplates)
		nginxGR.POST("/templates", nginx.CreateNginxConfigTemplate)
		nginxGR.PUT("/templates/:id", nginx.UpdateNginxConfigTemplate)
		nginxGR.DELETE("/templates/:id", nginx.DeleteNginxConfigTemplate)
		nginxGR.GET("/templates/:id", nginx.GetNginxConfigTemplate)

		// Nginx配置验证和部署
		nginxGR.POST("/configs/:config_id/validate", nginx.ValidateNginxConfig)
		nginxGR.POST("/configs/:config_id/deploy", nginx.DeployNginxConfig)
		nginxGR.POST("/servers/:id/reload", nginx.ReloadNginxServer)
		nginxGR.POST("/servers/:id/restart", nginx.RestartNginxServer)

		// Nginx日志管理
		nginxGR.GET("/servers/:id/logs", nginx.GetNginxServerLogs)
		nginxGR.GET("/servers/:id/access-logs", nginx.GetNginxServerAccessLogs)
		nginxGR.GET("/servers/:id/error-logs", nginx.GetNginxServerErrorLogs)

		// Nginx统计
		nginxGR.GET("/servers/:id/statistics", nginx.GetNginxServerStatistics)
		nginxGR.GET("/statistics/summary", nginx.GetNginxStatisticsSummary)
	}
}
