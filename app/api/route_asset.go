package api

import (
	"cmdb/app/api/asset"
	"cmdb/app/api/asset/k8s"
	"cmdb/app/api/assets"

	"github.com/gin-gonic/gin"
)

// setupAssetRoutes 设置资产管理相关路由
func setupAssetRoutes(apiV1GR *gin.RouterGroup) {
	// 资产管理路由（需要管理员权限）
	assetGRAdmin := apiV1GR.Group("/asset", adminRequired())
	{
		// 系统管理
		assetGRAdmin.GET("/systems", assets.GetAssetSystems)
		assetGRAdmin.POST("/systems", assets.CreateAssetSystem)
		assetGRAdmin.PUT("/systems/:id", assets.UpdateAssetSystem)
		assetGRAdmin.DELETE("/systems/:id", assets.DeleteAssetSystem)
		assetGRAdmin.GET("/systems/:id", assets.GetAssetSystem)

		// 节点管理
		assetGRAdmin.GET("/systems/:id/nodes", assets.GetAssetSystemNodes)
		assetGRAdmin.POST("/system/nodes", assets.CreateAssetNode)
		assetGRAdmin.PUT("/system/nodes/:id", assets.UpdateAssetNode)
		assetGRAdmin.DELETE("/system/nodes/:id", assets.DeleteAssetNode)
		assetGRAdmin.GET("/system/nodes/:id/assets", assets.GetAssetNodeAssets)

		// 主机管理
		assetGRAdmin.GET("/hosts", asset.GetHosts)
		assetGRAdmin.POST("/hosts", asset.CreateHost)
		assetGRAdmin.PUT("/hosts/:id", asset.UpdateHost)
		assetGRAdmin.DELETE("/hosts/:id", asset.DeleteHost)
		assetGRAdmin.GET("/all/hosts", asset.GetAllHosts)
		assetGRAdmin.GET("/host/:id/lbs", asset.GetLoadbalancersByHost)
		assetGRAdmin.GET("/host/:id/assets", asset.GetHostAssets)
		assetGRAdmin.PUT("/bat-set-monitor/hosts", asset.BatSetMonitorHost)

		// 主机变更日志
		assetGRAdmin.GET("/hosts/:id/changelogs", asset.GetHostChangeLogs)
		assetGRAdmin.POST("/hosts/:id/changelogs", asset.AddHostChangeLog)

		// 云账户管理
		assetGRAdmin.GET("/cloud-accounts", asset.GetCloudAccounts)
		assetGRAdmin.GET("/all/cloud-accounts", asset.GetAllCloudAccounts)
		assetGRAdmin.POST("/cloud-accounts", asset.AddCloudAccount)
		assetGRAdmin.PUT("/cloud-accounts/:id", asset.UpdateCloudAccount)
		assetGRAdmin.DELETE("/cloud-accounts/:id", asset.DeleteCloudAccount)
		assetGRAdmin.POST("/cloud-accounts/:id/sync/:asset", asset.SyncCloudAccountAsset)
		assetGRAdmin.POST("/cloud-accounts/:id/sync/monthly-bill", asset.SyncMonthlyBills)

		// 账单管理
		assetGRAdmin.GET("/monthly-bills/:id", asset.GetMonthlyBills)
		assetGRAdmin.GET("/monthly-bills/:id/amounts", asset.GetMonthlyBillAmounts)
		assetGRAdmin.GET("/monthly-bills/:id/resource-types/amounts", asset.GetMonthlyBillResourceTypeAmount)
		assetGRAdmin.GET("/monthly-bills/:id/resource-types/amounts/cycles", asset.GetMonthlyBillResourceTypeAmounts)
		assetGRAdmin.GET("/monthly-bills/cloud-type-monthly-bills", asset.GetCloudTypeMonthlyBills)

		// 数据中心管理
		assetGRAdmin.GET("/datacenters", asset.GetDatacenters)
		assetGRAdmin.GET("/all/datacenters", asset.GetAllDatacenters)
		assetGRAdmin.POST("/datacenters", asset.AddDatacenter)
		assetGRAdmin.PUT("/datacenters/:id", asset.UpdateDatacenter)
		assetGRAdmin.DELETE("/datacenters/:id", asset.DeleteDatacenter)

		// 业务管理
		assetGRAdmin.GET("/businesses", asset.GetBusinesses)
		assetGRAdmin.POST("/businesses", asset.AddBusiness)
		assetGRAdmin.PUT("/businesses/:id", asset.UpdateBusiness)
		assetGRAdmin.DELETE("/businesses/:id", asset.DeleteBusiness)
		assetGRAdmin.POST("/businesses/:id/set-resource-groups", asset.UpdateBusinessResourceGroups)
		assetGRAdmin.GET("/businesses/:id/monthly-bills", asset.GetBusinessMonthlyBills)

		// 标签管理
		assetGRAdmin.GET("/tags", asset.GetTags)
		assetGRAdmin.POST("/tags", asset.AddTag)
		assetGRAdmin.PUT("/tags/:id", asset.UpdateTag)
		assetGRAdmin.DELETE("/tags/:id", asset.DeleteTag)
		assetGRAdmin.GET("/tag/keys", asset.GetTagKeys)
		assetGRAdmin.GET("/all/tags", asset.GetAllTags)
		assetGRAdmin.POST("/tag/hosts", asset.BatTagHosts)

		// 负载均衡器
		assetGRAdmin.GET("/loadbalancers", asset.GetLoadbalancers)

		// DDOS防护
		assetGRAdmin.GET("/ddos/instances", asset.GetDDosProtections)
		assetGRAdmin.GET("/ddos/domains", asset.GetDDosDomains)
		assetGRAdmin.GET("/ddos/domains/:id/bps", asset.GetDDosDomainBps)
		assetGRAdmin.GET("/ddos/all/domains/bps", asset.GetAllDDosDomainBps)
		assetGRAdmin.POST("/ddos/domains/:id/sync/bps", asset.SyncDDosDomainBps)

		// 公网IP
		assetGRAdmin.GET("/public-ips", asset.GetPublicIPs)

		// 资源组
		assetGRAdmin.GET("/resource-groups", asset.GetResourceGroups)
		assetGRAdmin.GET("/resource-groups/:id/month/bills", asset.GetResourceGroupMonthlyBill)
		assetGRAdmin.GET("/all/resource-groups", asset.GetAllResourceGroups)

		// 子网
		assetGRAdmin.GET("/subnets", asset.GetSubNets)

		// 私有DNS
		assetGRAdmin.GET("/private-domains", asset.GetPrivateDomains)
		assetGRAdmin.GET("/private-domains/:domain_id/records", asset.GetPrivateDomainRecords)

		// 公有DNS
		assetGRAdmin.GET("/public-domains", asset.GetPublicDomains)
		assetGRAdmin.GET("/public-domains/:domain_id/records", asset.GetPublicDomainRecords)

		// K8s集群管理
		setupK8sRoutes(assetGRAdmin)
	}

	// 资产查询路由（需要登录）
	assetGRAuthed := apiV1GR.Group("/asset", loginRequired())
	{
		assetGRAuthed.GET("/all/businesses", asset.GetAllBusinesses)
	}
}

// setupK8sRoutes 设置K8s相关路由
func setupK8sRoutes(assetGRAdmin *gin.RouterGroup) {
	k8sGRAdmin := assetGRAdmin.Group("/k8s")
	{
		k8sGRAdmin.GET("/clusters", k8s.GetClusters)
		k8sGRAdmin.POST("/clusters", k8s.AddCluster)
		k8sGRAdmin.PUT("/clusters/:id", k8s.UpdateCluster)
		k8sGRAdmin.DELETE("/clusters/:id", k8s.DeleteCluster)
		k8sGRAdmin.GET("/clusters/:id/namespaces", k8s.GetClusterNamespaces)
		k8sGRAdmin.GET("/all/clusters", k8s.GetAllClusters)
		k8sGRAdmin.POST("/clusters/:id/sync", k8s.SyncK8s)
		k8sGRAdmin.GET("/nodes", k8s.GetNodes)
		k8sGRAdmin.GET("/workloads", k8s.GetWorkLoads)
		k8sGRAdmin.GET("/workloads/:id", k8s.GetWorkloadDetail)
		k8sGRAdmin.GET("/pods", k8s.GetPods)
		k8sGRAdmin.GET("/services", k8s.GetServices)
	}
}
