package api

import (
	"cmdb/app/api/audit"
	"cmdb/app/api/auth"
	"cmdb/app/api/dashboard"

	"github.com/gin-gonic/gin"
)

// setupAuthRoutes 设置认证相关路由
func setupAuthRoutes(apiV1GR *gin.RouterGroup) {
	// 仪表板路由（需要登录）
	dashboardGRAuthed := apiV1GR.Group("/dashboard", loginRequired())
	{
		dashboardGRAuthed.GET("/data", dashboard.GetDashboardData)
		dashboardGRAuthed.POST("/search", dashboard.Search)
	}

	// 用户认证路由（需要登录）
	authGRAuthed := apiV1GR.Group("/auth", loginRequired())
	{
		authGRAuthed.GET("/current_user", auth.GetCurrentUser)
		authGRAuthed.GET("/all/users", auth.GetAllUsers)
		authGRAuthed.GET("/all/groups", auth.GetAllGroups)
		authGRAuthed.PUT("/personal/phone", auth.UpdatePersonalPhone)
		authGRAuthed.PUT("/personal/password", auth.UpdatePersonalPassword)
		authGRAuthed.GET("/personal/info", auth.GetPersonalInfo)
	}

	// 用户管理路由（需要管理员权限）
	authGRAdmin := apiV1GR.Group("/auth", adminRequired())
	{
		authGRAdmin.GET("/users", auth.GetUsers)
		authGRAdmin.PUT("/users/:id", auth.EditUser)
		authGRAdmin.GET("/groups", auth.GetGroups)
		authGRAdmin.POST("/groups", auth.CreateGroup)
		authGRAdmin.PUT("/groups/:id", auth.UpdateGroup)
		authGRAdmin.DELETE("/groups/:id", auth.DeleteGroup)
		authGRAdmin.GET("/departments", auth.GetDepartments)
		authGRAdmin.POST("/batch/group", auth.BatGroupUsers)
		// API密钥管理
		authGRAdmin.GET("/keys", auth.GetKeys)
		authGRAdmin.POST("/keys", auth.CreateKey)
		authGRAdmin.GET("/keys/:id", auth.GetKeyInfo)
		authGRAdmin.PUT("/keys/:id", auth.UpdateKey)
		authGRAdmin.DELETE("/keys/:id", auth.DeleteKey)
		authGRAdmin.POST("keys/:id/grant", auth.GrantKeyAPIs)
		// 所有API对象接口
		authGRAdmin.GET("/all/apis", auth.GetAllAPIs)
	}

	// 审计日志路由（需要管理员权限）
	auditGRAdmin := apiV1GR.Group("/audit", adminRequired())
	{
		auditGRAdmin.GET("/login/logs", audit.GetLoginLogs)
		auditGRAdmin.GET("/op/logs", audit.GetOPLogs)
	}
}
