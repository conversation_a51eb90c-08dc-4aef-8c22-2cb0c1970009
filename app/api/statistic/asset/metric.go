package asset

import (
	"cmdb/app"
	"cmdb/app/service/statistic/asset"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// GetResourceMetrics 获取资源指标历史趋势
// @Summary 获取资源指标历史趋势
// @Description 获取指定资源类型、指标类型、资源名称和日期范围的指标历史趋势
// @Tags 资源指标
// @Accept json
// @Produce json
// @Param resource_type query string true "资源类型，可选值为 computer, cloud, region, data, network, optimizable"
// @Param metric_type query string true "指标类型，根据资源类型不同而不同"
// @Param name query string false "资源名称，当resource_type为cloud时表示云类型名称，当resource_type为region时表示区域名称，当resource_type为network时表示域名"
// @Param start_date query string true "开始日期，格式为 YYYY-MM-DD"
// @Param end_date query string true "结束日期，格式为 YYYY-MM-DD"
// @Success 200 {object} app.Response
// @Router /api/statistic/asset/metrics [get]
func GetResourceMetrics(c *gin.Context) {
	// 获取参数
	resourceType := c.Query("resource_type")
	metricType := c.Query("metric_type")
	name := c.Query("name")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	// 验证参数
	if resourceType == "" {
		app.FailedResponseMsg(c, "资源类型不能为空")
		return
	}

	if metricType == "" {
		app.FailedResponseMsg(c, "指标类型不能为空")
		return
	}

	if startDate == "" {
		app.FailedResponseMsg(c, "开始日期不能为空")
		return
	}

	if endDate == "" {
		app.FailedResponseMsg(c, "结束日期不能为空")
		return
	}

	// 当资源类型为cloud或region时，name不能为空
	if (resourceType == "cloud" || resourceType == "region") && name == "" {
		app.FailedResponseMsg(c, "资源名称不能为空")
		return
	}

	// 调用服务层函数
	metrics, err := asset.GetResourceMetrics(asset.ResourceType(resourceType), asset.MetricType(metricType), name, startDate, endDate)
	if err != nil {
		app.FailedResponseMsg(c, err.Error())
		return
	}

	// 返回结果
	app.SuccessResponseData(c, metrics)
}

// GetLatestResourceMetric 获取最新的资源指标值
// @Summary 获取最新的资源指标值
// @Description 获取指定资源类型、指标类型和资源名称的最新指标值
// @Tags 资源指标
// @Accept json
// @Produce json
// @Param resource_type query string true "资源类型，可选值为 computer, cloud, region, data, network, optimizable"
// @Param metric_type query string true "指标类型，根据资源类型不同而不同"
// @Param name query string false "资源名称，当resource_type为cloud时表示云类型名称，当resource_type为region时表示区域名称，当resource_type为network时表示域名"
// @Success 200 {object} app.Response
// @Router /api/statistic/asset/metrics/latest [get]
func GetLatestResourceMetric(c *gin.Context) {
	// 获取参数
	resourceType := c.Query("resource_type")
	metricType := c.Query("metric_type")
	name := c.Query("name")

	// 验证参数
	if resourceType == "" {
		app.FailedResponseMsg(c, "资源类型不能为空")
		return
	}

	if metricType == "" {
		app.FailedResponseMsg(c, "指标类型不能为空")
		return
	}

	// 当资源类型为cloud或region时，name不能为空
	if (resourceType == "cloud" || resourceType == "region") && name == "" {
		app.FailedResponseMsg(c, "资源名称不能为空")
		return
	}

	// 调用服务层函数
	value, err := asset.GetLatestResourceMetric(asset.ResourceType(resourceType), asset.MetricType(metricType), name)
	if err != nil {
		app.FailedResponseMsg(c, err.Error())
		return
	}

	// 返回结果
	app.SuccessResponseData(c, value)
}

// GetResourceMetricsByDateRange 获取指定日期范围内的资源指标历史趋势
// @Summary 获取指定日期范围内的资源指标历史趋势
// @Description 获取指定资源类型、指标类型、资源名称和天数的指标历史趋势
// @Tags 资源指标
// @Accept json
// @Produce json
// @Param resource_type query string true "资源类型，可选值为 computer, cloud, region, data, network, optimizable"
// @Param metric_type query string true "指标类型，根据资源类型不同而不同"
// @Param name query string false "资源名称，当resource_type为cloud时表示云类型名称，当resource_type为region时表示区域名称，当resource_type为network时表示域名"
// @Param days query int true "天数，表示获取最近多少天的数据"
// @Success 200 {object} app.Response
// @Router /api/statistic/asset/metrics/range [get]
func GetResourceMetricsByDateRange(c *gin.Context) {
	// 获取参数
	resourceType := c.Query("resource_type")
	metricType := c.Query("metric_type")
	name := c.Query("name")
	daysStr := c.Query("days")

	// 验证参数
	if resourceType == "" {
		app.FailedResponseMsg(c, "资源类型不能为空")
		return
	}

	if metricType == "" {
		app.FailedResponseMsg(c, "指标类型不能为空")
		return
	}

	if daysStr == "" {
		app.FailedResponseMsg(c, "天数不能为空")
		return
	}

	// 当资源类型为cloud或region时，name不能为空
	if (resourceType == "cloud" || resourceType == "region") && name == "" {
		app.FailedResponseMsg(c, "资源名称不能为空")
		return
	}

	// 将天数转换为整数
	days, err := strconv.Atoi(daysStr)
	if err != nil {
		app.FailedResponseMsg(c, "天数必须为整数")
		return
	}

	// 调用服务层函数
	metrics, err := asset.GetResourceMetricsByDateRange(asset.ResourceType(resourceType), asset.MetricType(metricType), name, days)
	if err != nil {
		app.FailedResponseMsg(c, err.Error())
		return
	}

	// 返回结果
	app.SuccessResponseData(c, metrics)
}

// GetAllCloudTypes 获取所有云类型
// @Summary 获取所有云类型
// @Description 获取所有云类型
// @Tags 资源指标
// @Accept json
// @Produce json
// @Success 200 {object} app.Response
// @Router /api/statistic/asset/metrics/cloud-types [get]
func GetAllCloudTypes(c *gin.Context) {
	// 调用服务层函数
	cloudTypes, err := asset.GetAllCloudTypes()
	if err != nil {
		app.FailedResponseMsg(c, err.Error())
		return
	}

	// 返回结果
	app.SuccessResponseData(c, cloudTypes)
}

// GetAllRegionNames 获取所有区域名称
// @Summary 获取所有区域名称
// @Description 获取所有区域名称
// @Tags 资源指标
// @Accept json
// @Produce json
// @Success 200 {object} app.Response
// @Router /api/statistic/asset/metrics/region-names [get]
func GetAllRegionNames(c *gin.Context) {
	// 调用服务层函数
	regionNames, err := asset.GetAllRegionNames()
	if err != nil {
		app.FailedResponseMsg(c, err.Error())
		return
	}

	// 返回结果
	app.SuccessResponseData(c, regionNames)
}

// GetAllDomainNames 获取所有域名
// @Summary 获取所有域名
// @Description 获取所有域名
// @Tags 资源指标
// @Accept json
// @Produce json
// @Success 200 {object} app.Response
// @Router /api/statistic/asset/metrics/domain-names [get]
func GetAllDomainNames(c *gin.Context) {
	// 调用服务层函数
	domainNames, err := asset.GetAllDomainNames()
	if err != nil {
		app.FailedResponseMsg(c, err.Error())
		return
	}

	// 返回结果
	app.SuccessResponseData(c, domainNames)
}

// GetAllDataResourceTypes 获取所有数据资源类型
// @Summary 获取所有数据资源类型
// @Description 获取所有数据资源类型
// @Tags 资源指标
// @Accept json
// @Produce json
// @Success 200 {object} app.Response
// @Router /api/statistic/asset/metrics/data-resource-types [get]
func GetAllDataResourceTypes(c *gin.Context) {
	// 调用服务层函数
	dataResourceTypes, err := asset.GetAllDataResourceTypes()
	if err != nil {
		app.FailedResponseMsg(c, err.Error())
		return
	}

	// 将 MetricType 转换为字符串
	result := make([]string, len(dataResourceTypes))
	for i, t := range dataResourceTypes {
		result[i] = string(t)
	}

	// 返回结果
	app.SuccessResponseData(c, result)
}

// GetAllOptimizableResourceTypes 获取所有可优化资源类型
// @Summary 获取所有可优化资源类型
// @Description 获取所有可优化资源类型
// @Tags 资源指标
// @Accept json
// @Produce json
// @Success 200 {object} app.Response
// @Router /api/statistic/asset/metrics/optimizable-resource-types [get]
func GetAllOptimizableResourceTypes(c *gin.Context) {
	// 调用服务层函数
	optimizableResourceTypes, err := asset.GetAllOptimizableResourceTypes()
	if err != nil {
		app.FailedResponseMsg(c, err.Error())
		return
	}

	// 将 MetricType 转换为字符串
	result := make([]string, len(optimizableResourceTypes))
	for i, t := range optimizableResourceTypes {
		result[i] = string(t)
	}

	// 返回结果
	app.SuccessResponseData(c, result)
}

// ===== 以下是为了兼容旧的statistics API而添加的函数 =====

// GetMonthlyAssetStat 处理获取月度资产统计数据的请求
// 兼容旧的 /api/v1/statistics/asset/stat 接口
func GetMonthlyAssetStat(c *gin.Context) {
	month := c.Query("month")
	if month == "" {
		month := time.Now().AddDate(0, -1, 0).Format("2006-01")
		_ = month // 避免未使用变量错误
	}
	// 这里需要调用statistics服务，但为了避免循环依赖，暂时返回空数据
	// TODO: 将statistics服务的功能迁移到statistic服务中
	app.SuccessResponseData(c, map[string]interface{}{
		"message": "此接口已迁移，请使用新的统计接口",
	})
}

// UpdateDataAsset 处理更新特定资产数据记录的请求
// 兼容旧的 /api/v1/statistics/asset/update/:id/database 接口
func UpdateDataAsset(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	_ = id // 避免未使用变量错误

	// TODO: 实现数据更新逻辑
	app.SuccessResponseMsg(c, "此接口已迁移，请使用新的统计接口")
}

// StatAssetNow 触发对上个月资产数据的即时统计计算
// 兼容旧的 /api/v1/statistics/asset/stat/now 接口
func StatAssetNow(c *gin.Context) {
	// TODO: 实现统计逻辑
	app.SuccessResponseMsg(c, "此接口已迁移，请使用新的统计接口")
}

// GetAssetMetrics 获取资产指标数据
// 兼容旧的 /api/v1/statistics/asset/stat/metrics 接口
func GetAssetMetrics(c *gin.Context) {
	start := c.Query("start")
	if start == "" {
		start = time.Now().AddDate(0, -12, 0).Format("2006-01")
	}
	end := c.Query("end")
	if end == "" {
		end = time.Now().Format("2006-01")
	}
	assetType := c.Query("asset_type")
	metricType := c.Query("metric_type")
	name := c.Query("name")

	_ = start
	_ = end
	_ = assetType
	_ = metricType
	_ = name

	// TODO: 实现指标获取逻辑
	app.SuccessResponseData(c, []interface{}{})
}
