package api

import (
	"cmdb/app"
	"net/http"

	"github.com/gin-gonic/gin"
)

// GenRoute 生成路由
func GenRoute() (r *gin.Engine, err error) {
	r = gin.New()
	r.Use(gin.Recovery())
	r.TrustedPlatform = gin.PlatformGoogleAppEngine
	if app.Conf().LogLevel == "debug" {
		// debug级别
		r.Use(gin.Logger())
	} else {
		// 关闭终端输出，写入日志文件
		gin.SetMode(gin.ReleaseMode)
	}
	r.Use(Cors())

	// 设置基础路由（不需要认证的路由）
	setupBaseRoutes(r)

	// 设置API v1路由组
	apiV1GR := setupAPIV1Routes(r)

	// 设置各个功能模块的路由
	setupAuthRoutes(apiV1GR)
	setupAssetRoutes(apiV1GR)
	setupStatisticRoutes(apiV1GR)
	setupAppOpsRoutes(apiV1GR)
	setupDatabaseRoutes(apiV1GR)
	setupTaskRoutes(apiV1GR)
	setupNoticeRoutes(apiV1GR)
	setupNoteRoutes(apiV1GR)
	setupWorkflowRoutes(apiV1GR)
	setupMonitorRoutes(apiV1GR)
	setupSettingRoutes(apiV1GR)

	// 设置API v2路由组
	setupAPIV2Routes(r)

	return
}
