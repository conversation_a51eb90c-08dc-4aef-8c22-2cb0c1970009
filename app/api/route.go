package api

import (
	"cmdb/app"
	"cmdb/app/api/appops"
	"cmdb/app/api/appops/domain"
	"cmdb/app/api/appops/gitcode"
	"cmdb/app/api/appops/nginx"
	"cmdb/app/api/asset"
	"cmdb/app/api/asset/k8s"
	"cmdb/app/api/assets"
	"cmdb/app/api/audit"
	"cmdb/app/api/auth"
	"cmdb/app/api/dashboard"
	"cmdb/app/api/database/mongodb"
	"cmdb/app/api/database/mysql"
	"cmdb/app/api/database/redis"
	"cmdb/app/api/database/slowlog"
	"cmdb/app/api/monitor"
	"cmdb/app/api/note"
	"cmdb/app/api/notice"
	"cmdb/app/api/robot"
	"cmdb/app/api/setting"
	statisticAsset "cmdb/app/api/statistic/asset"
	bill "cmdb/app/api/statistic/bill"
	statisticMonitor "cmdb/app/api/statistic/monitor"
	"cmdb/app/api/task"
	"cmdb/app/api/upload"
	"cmdb/app/api/workflow"
	"net/http"

	v2 "cmdb/app/api/v2"

	_ "cmdb/app/service/metric"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus/promhttp"
)

// GenRoute 生成路由
func GenRoute() (r *gin.Engine, err error) {
	r = gin.New()
	r.Use(gin.Recovery())
	r.TrustedPlatform = gin.PlatformGoogleAppEngine
	if app.Conf().LogLevel == "debug" {
		// debug级别
		r.Use(gin.Logger())
	} else {
		// 关闭终端输出，写入日志文件
		gin.SetMode(gin.ReleaseMode)
	}
	r.Use(Cors())
	prometheusHandler := promhttp.Handler()
	r.GET("/metrics", gin.WrapH(prometheusHandler))
	r.GET("/machineuse", dashboard.MachineUse)
	r.Any("/ping", func(c *gin.Context) { c.String(http.StatusOK, "PONG") })
	// GR来缩写GroupRouter 权限加在名称后面 Authed、Admin
	r.POST("/login", auth.Login)
	r.POST("/access_key_login", auth.KeyAuth)
	r.POST("/dingchat/:usage", robot.DingTalkChatAPI)
	r.POST("/api/refresh-token", auth.RefreshToken)
	r.POST("/api/oalogin", auth.OAAuth)
	r.PUT("/api/gateway/login", auth.OAGatewayLogin)
	apiV1GR := r.Group("/api/v1")
	apiV1GR.GET("/get-async-routes", func(c *gin.Context) {
		// 获取当前用户有权限的异步路由
		// 暂时返回空
		c.JSON(http.StatusOK, []string{})
	}, loginRequired())
	dashboardGRAuthed := apiV1GR.Group("/dashboard", loginRequired())
	{
		dashboardGRAuthed.GET("/data", dashboard.GetDashboardData)
		dashboardGRAuthed.POST("/search", dashboard.Search)
	}
	authGRAuthed := apiV1GR.Group("/auth", loginRequired())
	{
		authGRAuthed.GET("/current_user", auth.GetCurrentUser)
		authGRAuthed.GET("/all/users", auth.GetAllUsers)
		authGRAuthed.GET("/all/groups", auth.GetAllGroups)
		authGRAuthed.PUT("/personal/phone", auth.UpdatePersonalPhone)
		authGRAuthed.PUT("/personal/password", auth.UpdatePersonalPassword)
		authGRAuthed.GET("/personal/info", auth.GetPersonalInfo)
	}
	authGRAdmin := apiV1GR.Group("/auth", adminRequired())
	{
		authGRAdmin.GET("/users", auth.GetUsers)
		authGRAdmin.PUT("/users/:id", auth.EditUser)
		authGRAdmin.GET("/groups", auth.GetGroups)
		authGRAdmin.POST("/groups", auth.CreateGroup)
		authGRAdmin.PUT("/groups/:id", auth.UpdateGroup)
		authGRAdmin.DELETE("/groups/:id", auth.DeleteGroup)
		authGRAdmin.GET("/departments", auth.GetDepartments)
		authGRAdmin.POST("/batch/group", auth.BatGroupUsers)
		// keyds
		authGRAdmin.GET("/keys", auth.GetKeys)
		authGRAdmin.POST("/keys", auth.CreateKey)
		authGRAdmin.GET("/keys/:id", auth.GetKeyInfo)
		authGRAdmin.PUT("/keys/:id", auth.UpdateKey)
		authGRAdmin.DELETE("/keys/:id", auth.DeleteKey)
		authGRAdmin.POST("keys/:id/grant", auth.GrantKeyAPIs)
		// 所有api对象接口
		authGRAdmin.GET("/all/apis", auth.GetAllAPIs)
	}
	auditGRAdmin := apiV1GR.Group("/audit", adminRequired())
	{
		auditGRAdmin.GET("/login/logs", audit.GetLoginLogs)
		auditGRAdmin.GET("/op/logs", audit.GetOPLogs)
	}
	assetGRAdmin := apiV1GR.Group("/asset", adminRequired())
	{
		// 系统
		assetGRAdmin.GET("/systems", assets.GetAssetSystems)
		assetGRAdmin.POST("/systems", assets.CreateAssetSystem)
		assetGRAdmin.PUT("/systems/:id", assets.UpdateAssetSystem)
		assetGRAdmin.DELETE("/systems/:id", assets.DeleteAssetSystem)
		assetGRAdmin.GET("/systems/:id", assets.GetAssetSystem)
		// 节点
		assetGRAdmin.GET("/systems/:id/nodes", assets.GetAssetSystemNodes)
		assetGRAdmin.POST("/system/nodes", assets.CreateAssetNode)
		assetGRAdmin.PUT("/system/nodes/:id", assets.UpdateAssetNode)
		assetGRAdmin.DELETE("/system/nodes/:id", assets.DeleteAssetNode)
		assetGRAdmin.GET("/system/nodes/:id/assets", assets.GetAssetNodeAssets)
		// hosts
		assetGRAdmin.GET("/hosts", asset.GetHosts)
		assetGRAdmin.POST("/hosts", asset.CreateHost)
		assetGRAdmin.PUT("/hosts/:id", asset.UpdateHost)
		assetGRAdmin.DELETE("/hosts/:id", asset.DeleteHost)
		assetGRAdmin.GET("/all/hosts", asset.GetAllHosts)
		assetGRAdmin.GET("/host/:id/lbs", asset.GetLoadbalancersByHost)
		assetGRAdmin.GET("/host/:id/assets", asset.GetHostAssets)
		assetGRAdmin.PUT("/bat-set-monitor/hosts", asset.BatSetMonitorHost)
		// changlog
		assetGRAdmin.GET("/hosts/:id/changelogs", asset.GetHostChangeLogs)
		assetGRAdmin.POST("/hosts/:id/changelogs", asset.AddHostChangeLog)

		// 云账户
		assetGRAdmin.GET("/cloud-accounts", asset.GetCloudAccounts)
		assetGRAdmin.GET("/all/cloud-accounts", asset.GetAllCloudAccounts)
		assetGRAdmin.POST("/cloud-accounts", asset.AddCloudAccount)
		assetGRAdmin.PUT("/cloud-accounts/:id", asset.UpdateCloudAccount)
		assetGRAdmin.DELETE("/cloud-accounts/:id", asset.DeleteCloudAccount)
		assetGRAdmin.POST("/cloud-accounts/:id/sync/:asset", asset.SyncCloudAccountAsset)
		assetGRAdmin.POST("/cloud-accounts/:id/sync/monthly-bill", asset.SyncMonthlyBills)
		// 账单
		assetGRAdmin.GET("/monthly-bills/:id", asset.GetMonthlyBills)
		assetGRAdmin.GET("/monthly-bills/:id/amounts", asset.GetMonthlyBillAmounts)
		assetGRAdmin.GET("/monthly-bills/:id/resource-types/amounts", asset.GetMonthlyBillResourceTypeAmount)
		assetGRAdmin.GET("/monthly-bills/:id/resource-types/amounts/cycles", asset.GetMonthlyBillResourceTypeAmounts)
		assetGRAdmin.GET("/monthly-bills/cloud-type-monthly-bills", asset.GetCloudTypeMonthlyBills)
		// datacenters
		assetGRAdmin.GET("/datacenters", asset.GetDatacenters)
		assetGRAdmin.GET("/all/datacenters", asset.GetAllDatacenters)
		assetGRAdmin.POST("/datacenters", asset.AddDatacenter)
		assetGRAdmin.PUT("/datacenters/:id", asset.UpdateDatacenter)
		assetGRAdmin.DELETE("/datacenters/:id", asset.DeleteDatacenter)

		// business
		assetGRAdmin.GET("/businesses", asset.GetBusinesses)
		assetGRAdmin.POST("/businesses", asset.AddBusiness)
		assetGRAdmin.PUT("/businesses/:id", asset.UpdateBusiness)
		assetGRAdmin.DELETE("/businesses/:id", asset.DeleteBusiness)
		assetGRAdmin.POST("/businesses/:id/set-resource-groups", asset.UpdateBusinessResourceGroups)
		assetGRAdmin.GET("/businesses/:id/monthly-bills", asset.GetBusinessMonthlyBills)
		// 标签
		assetGRAdmin.GET("/tags", asset.GetTags)
		assetGRAdmin.POST("/tags", asset.AddTag)
		assetGRAdmin.PUT("/tags/:id", asset.UpdateTag)
		assetGRAdmin.DELETE("/tags/:id", asset.DeleteTag)
		assetGRAdmin.GET("/tag/keys", asset.GetTagKeys)
		assetGRAdmin.GET("/all/tags", asset.GetAllTags)
		assetGRAdmin.POST("/tag/hosts", asset.BatTagHosts)
		// loadbalancer
		assetGRAdmin.GET("/loadbalancers", asset.GetLoadbalancers)
		// DDOS
		assetGRAdmin.GET("/ddos/instances", asset.GetDDosProtections)
		assetGRAdmin.GET("/ddos/domains", asset.GetDDosDomains)
		assetGRAdmin.GET("/ddos/domains/:id/bps", asset.GetDDosDomainBps)
		assetGRAdmin.GET("/ddos/all/domains/bps", asset.GetAllDDosDomainBps)
		assetGRAdmin.POST("/ddos/domains/:id/sync/bps", asset.SyncDDosDomainBps)
		// public_ip
		assetGRAdmin.GET("/public-ips", asset.GetPublicIPs)
		// 资源组
		assetGRAdmin.GET("/resource-groups", asset.GetResourceGroups)
		assetGRAdmin.GET("/resource-groups/:id/month/bills", asset.GetResourceGroupMonthlyBill)
		assetGRAdmin.GET("/all/resource-groups", asset.GetAllResourceGroups)
		// 子网
		assetGRAdmin.GET("/subnets", asset.GetSubNets)
		// private dns
		assetGRAdmin.GET("/private-domains", asset.GetPrivateDomains)
		assetGRAdmin.GET("/private-domains/:domain_id/records", asset.GetPrivateDomainRecords)
		// public dns
		assetGRAdmin.GET("/public-domains", asset.GetPublicDomains)
		assetGRAdmin.GET("/public-domains/:domain_id/records", asset.GetPublicDomainRecords)
		// k8s集群管理
		k8sGRAdmin := assetGRAdmin.Group("/k8s")
		{
			k8sGRAdmin.GET("/clusters", k8s.GetClusters)
			k8sGRAdmin.POST("/clusters", k8s.AddCluster)
			k8sGRAdmin.PUT("/clusters/:id", k8s.UpdateCluster)
			k8sGRAdmin.DELETE("/clusters/:id", k8s.DeleteCluster)
			k8sGRAdmin.GET("/clusters/:id/namespaces", k8s.GetClusterNamespaces)
			k8sGRAdmin.GET("/all/clusters", k8s.GetAllClusters)
			k8sGRAdmin.POST("/clusters/:id/sync", k8s.SyncK8s)
			k8sGRAdmin.GET("/nodes", k8s.GetNodes)
			k8sGRAdmin.GET("/workloads", k8s.GetWorkLoads)
			k8sGRAdmin.GET("/workloads/:id", k8s.GetWorkloadDetail)
			k8sGRAdmin.GET("/pods", k8s.GetPods)
			k8sGRAdmin.GET("/services", k8s.GetServices)
		}
	}
	assetGRAuthed := apiV1GR.Group("/asset", loginRequired())
	{
		assetGRAuthed.GET("/all/businesses", asset.GetAllBusinesses)
	}
	databaseGRAdmin := apiV1GR.Group("/database", adminRequired())
	{
		databaseGRAdmin.GET("/mysql/instances", mysql.GetInstances)
		databaseGRAdmin.POST("/mysql/instances", mysql.AddInstance)
		databaseGRAdmin.POST("/mysql/instances/test/connection", mysql.TestConnection)
		databaseGRAdmin.PUT("/mysql/instances/:id", mysql.UpdateInstance)
		databaseGRAdmin.DELETE("/mysql/instances/:id", mysql.DeleteInstance)
		databaseGRAdmin.GET("/mysql/instances/:id/tables", mysql.GetInstanceTables)
		databaseGRAdmin.PUT("/mysql/instances/:id/sync", mysql.SyncInstanceMetadata)
		databaseGRAdmin.GET("/mysql/instances/:id/length", mysql.GetInstanceLengthHistories)
		databaseGRAdmin.GET("/mysql/instances/:id/schema/:name/length", mysql.GetInstanceSchemaLengthHistories)
		databaseGRAdmin.GET("/mysql/instances/:id/table/:schema/:name/length", mysql.GetInstanceTableLengthHistories)
		databaseGRAdmin.POST("/mysql/tag/instances", mysql.TagInstaces)
		// redis
		databaseGRAdmin.GET("/redis/instances", redis.GetInstances)
		databaseGRAdmin.POST("/redis/instances", redis.CreateRedisInstance)
		databaseGRAdmin.PUT("/redis/instances/:id", redis.UpdateInstance)
		databaseGRAdmin.DELETE("/redis/instances/:id", redis.DeleteInstance)
		databaseGRAdmin.POST("/redis/instances/test/connection", redis.TestConnection)
		databaseGRAdmin.POST("/redis/instances/sync", redis.SyncRedisInstanceFromPrometheus)
		databaseGRAdmin.POST("/redis/tag/instances", redis.TagInstaces)
		// backup
		databaseGRAdmin.GET("/mysql/backups", mysql.GetBackups)
		databaseGRAdmin.PUT("/mysql/check/today/backups", mysql.CheckTodyBackup)
		// 	项目
		databaseGRAdmin.GET("/mysql/projects", mysql.GetProjects)
		databaseGRAdmin.POST("/mysql/projects", mysql.CreateProject)
		databaseGRAdmin.PUT("/mysql/projects/:id", mysql.UpdateProject)
		databaseGRAdmin.PUT("/mysql/project/sync_dbs", mysql.SyncProjectsDBs)
		databaseGRAdmin.DELETE("/mysql/projects/:id", mysql.DeleteProject)
		// mongodb
		databaseGRAdmin.GET("/mongodb/instances", mongodb.GetInstances)
		databaseGRAdmin.POST("/mongodb/instances", mongodb.CreateInstance)
		databaseGRAdmin.PUT("/mongodb/instances/:id", mongodb.UpdateInstance)
		databaseGRAdmin.DELETE("/mongodb/instances/:id", mongodb.DeleteInstance)
		// slowlog
		databaseGRAdmin.GET("/slowlog/stores", slowlog.GetSlowlogStores)
		databaseGRAdmin.POST("/slowlog/stores", slowlog.CreateSlowlogStore)
		databaseGRAdmin.PUT("/slowlog/stores/:id", slowlog.UpdateSlowlogStore)
		databaseGRAdmin.DELETE("/slowlog/stores/:id", slowlog.DeleteSlowlogStore)
		databaseGRAdmin.GET("/slowlog/:id/mysql", slowlog.GetMySQLSlowLogs)
		databaseGRAdmin.GET("/slowlog/:id/mysql/dml", slowlog.GetMySQLDmlLogs)
		databaseGRAdmin.GET("/slowlog/:id/mysql/statistics", slowlog.GetMySQLSlowLogStatistics)
		databaseGRAdmin.GET("/slowlog/:id/mongodb", slowlog.GetMongoDBSlowLogs)
		databaseGRAdmin.GET("/slowlog/:id/mongodb/statistics", slowlog.GetMongoDBSlowLogStatistics)
		databaseGRAdmin.GET("/slowlog/:id/redis/bigkeys", slowlog.GetRedisBigKeys)
		databaseGRAdmin.GET("/slowlog/:id/redis/bigkeys/statistics", slowlog.GetRedisBigKeysStringStatistics)
		databaseGRAdmin.GET("/slowlog/:id/:db_type/project/statistics", slowlog.GetProjectStatistics)
	}
	// 登陆权限即可获取的路由
	databaseGRAuthed := apiV1GR.Group("/database", loginRequired())
	{
		databaseGRAuthed.GET("/mysql/all/projects", mysql.GetAllProjects)
		databaseGRAuthed.GET("/mongodb/all/instances", mongodb.GetAllInstances)
		databaseGRAuthed.GET("/slowlog/all/stores", slowlog.GetAllSlowlogStores)
	}
	// appops
	appopsGRAdmin := apiV1GR.Group("/appops", adminRequired())
	{
		// domain
		appopsGRAdmin.GET("/domains", domain.GetDomains)
		appopsGRAdmin.POST("/domains", domain.AddDomain)
		appopsGRAdmin.PUT("/domains/:id", domain.UpdateDomain)
		appopsGRAdmin.DELETE("/domains/:id", domain.DeleteDomain)
		appopsGRAdmin.GET("/all/domains", domain.GetAllDomains)
		// gitcode
		gitcodeGRAdmin := appopsGRAdmin.Group("/gitcode")
		{
			gitcodeGRAdmin.GET("/gitlabs", gitcode.GetGitlabs)
			gitcodeGRAdmin.POST("/gitlabs", gitcode.CreateGitlab)
			gitcodeGRAdmin.PUT("/gitlabs/:id", gitcode.UpdateGitlab)
			gitcodeGRAdmin.DELETE("/gitlabs/:id", gitcode.DeleteGitlab)
			gitcodeGRAdmin.POST("/gitlabs/:id/sync/groups", gitcode.SyncGroups)
			gitcodeGRAdmin.POST("/gitlabs/:id/sync/projects", gitcode.SyncProjects)
			gitcodeGRAdmin.GET("/groups", gitcode.GetGroups)
		}
		// harbor
		harborGRAdmin := appopsGRAdmin.Group("/harbors")
		{
			harborGRAdmin.GET("/", appops.GetHarbors)
			harborGRAdmin.POST("/", appops.CreateHarbor)
			harborGRAdmin.PUT("/:id", appops.UpdateHarbor)
			harborGRAdmin.DELETE("/:id", appops.DeleteHarbor)
		}
		// nginx
		ngrGRAdmin := appopsGRAdmin.Group("/nginx")
		{
			ngrGRAdmin.GET("/backends", nginx.GetNginxBackends)
			ngrGRAdmin.POST("/sync", nginx.SyncNginxs)
		}
	}
	appopsGRAuthed := apiV1GR.Group("/appops", loginRequired())
	{
		// harbor
		appopsGRAuthed.GET("/all/harbors", appops.GetAllHarbors)
		// gitcode
		gitcodeGRAuthed := appopsGRAuthed.Group("/gitcode")
		{
			gitcodeGRAuthed.GET("/all/gitlabs", gitcode.GetAllGitlabs)
			gitcodeGRAuthed.GET("/all/projects", gitcode.GetAllProjects)
			gitcodeGRAuthed.GET("/gitlabs/:id/groups", gitcode.GetGitlabGroups)
			gitcodeGRAuthed.GET("/groups/:id/projects", gitcode.GetGitlabProjects)
			gitcodeGRAuthed.GET("/projects", gitcode.GetProjects)
		}
	}
	statisticsGRAmin := apiV1GR.Group("/statistics")
	{
		statisticsGRAmin.GET("/asset/stat", statistics.GetMonthlyAssetStat)
		statisticsGRAmin.PUT("/asset/update/:id/database", statistics.UpdateDataAsset)
		statisticsGRAmin.POST("/asset/stat/now", statistics.StatAsssetNow)
		statisticsGRAmin.GET("/asset/stat/metrics", statistics.GetAssetMetrics)
	}
	statisticGRAmin := apiV1GR.Group("/statistic", adminRequired())
	{
		statisticGRAmin.POST("/stat", statisticAsset.GenStat)
		statisticGRAmin.GET("/report/daily", statisticAsset.GetDailyReport)
		statisticGRAmin.GET("/report/monthly", statisticAsset.GetMonthlyReport)
		statisticGRAmin.POST("/report/daily", statisticAsset.GenDailyReport)
		statisticGRAmin.POST("/report/monthly", statisticAsset.GenMonthlyReport)

		// 资源指标相关接口
		statisticGRAmin.GET("/asset/metrics", statisticAsset.GetResourceMetrics)
		statisticGRAmin.GET("/asset/metrics/latest", statisticAsset.GetLatestResourceMetric)
		statisticGRAmin.GET("/asset/metrics/range", statisticAsset.GetResourceMetricsByDateRange)
		statisticGRAmin.GET("/asset/metrics/cloud-types", statisticAsset.GetAllCloudTypes)
		statisticGRAmin.GET("/asset/metrics/region-names", statisticAsset.GetAllRegionNames)
		statisticGRAmin.GET("/asset/metrics/domain-names", statisticAsset.GetAllDomainNames)
		statisticGRAmin.GET("/asset/metrics/data-resource-types", statisticAsset.GetAllDataResourceTypes)
		statisticGRAmin.GET("/asset/metrics/optimizable-resource-types", statisticAsset.GetAllOptimizableResourceTypes)

		// 账单统计相关接口
		// 账号级别账单统计
		statisticGRAmin.GET("/bill/accounts", bill.GetAccountBills)
		statisticGRAmin.GET("/bill/accounts/range", bill.GetAccountBillsByDateRange)
		statisticGRAmin.GET("/bill/accounts/cloud-type", bill.GetAccountBillsByCloudType)
		// 云服务商账单统计
		statisticGRAmin.GET("/bill/clouds", bill.GetCloudBills)
		statisticGRAmin.GET("/bill/clouds/range", bill.GetCloudBillsByDateRange)
		statisticGRAmin.GET("/bill/clouds/summary", bill.GetCloudBillsSummary)
		// 资源组级别账单统计
		statisticGRAmin.GET("/bill/resource-groups", bill.GetResourceGroupBills)
		statisticGRAmin.GET("/bill/resource-groups/range", bill.GetResourceGroupBillsByDateRange)
		statisticGRAmin.GET("/bill/resource-groups/cloud-type", bill.GetResourceGroupBillsByCloudType)

		// 监控告警统计相关接口
		// 夜莺(n9e)告警统计
		statisticGRAmin.GET("/monitor/n9e/alerts", statisticMonitor.GetAlertStatistics)
		statisticGRAmin.GET("/monitor/n9e/alerts/by-instance", statisticMonitor.GetAlertStatisticsByInstance)
		statisticGRAmin.GET("/monitor/n9e/alerts/by-rule", statisticMonitor.GetAlertStatisticsByRule)
		statisticGRAmin.GET("/monitor/n9e/alerts/by-cluster", statisticMonitor.GetAlertStatisticsByCluster)
		statisticGRAmin.GET("/monitor/n9e/alerts/current", statisticMonitor.GetCurrentAlerts)
	}
	taskGRAdmin := apiV1GR.Group("/task", adminRequired())
	{
		taskGRAdmin.GET("/batch-templates", task.GetBatchTemplates)
		taskGRAdmin.POST("/batch-templates", task.CreateBatchTemplate)
		taskGRAdmin.PUT("/batch-templates/:id", task.UpdateBatchTemplate)
		taskGRAdmin.DELETE("/batch-templates/:id", task.DeleteBatchTemplate)
		taskGRAdmin.GET("/all/batch-templates", task.GetAllBatchTemplates)
		// 批量任务
		taskGRAdmin.GET("/batch-tasks", task.GetBatchTasks)
		taskGRAdmin.POST("/batch-tasks", task.CreateBatchTask)
		// 单个任务执行
		taskGRAdmin.POST("/batch-tasks/:id/run", task.RunBatchTask)
		taskGRAdmin.POST("/batch-tasks/:id/rerun", task.ReRunBatchTask)
		// 批量任务执行
		taskGRAdmin.POST("/batch-task/batch-run", task.BatRunBatchTask)
		taskGRAdmin.DELETE("/batch-tasks/:id", task.DeleteBatchTask)
		taskGRAdmin.GET("/batch-tasks/:id", task.GetBatchTask)
		taskGRAdmin.GET("/jobs", task.GetJobs)
		taskGRAdmin.GET("/job/logs", task.GetJobLogs)
	}
	noticeGRAdmin := apiV1GR.Group("/notice", adminRequired())
	{
		noticeGRAdmin.GET("/contacts", notice.GetContacts)
		noticeGRAdmin.POST("/contacts", notice.CreateContact)
		noticeGRAdmin.PUT("/contacts/:id", notice.UpdateContact)
		noticeGRAdmin.DELETE("/contacts/:id", notice.DeleteContact)
		noticeGRAdmin.GET("/all/contacts", notice.GetAllContacts)
		noticeGRAdmin.GET("/contact-groups", notice.GetContactGroups)
		noticeGRAdmin.POST("/contact-groups", notice.AddContactGroup)
		noticeGRAdmin.PUT("/contact-groups/:id", notice.UpdateContactGroup)
		noticeGRAdmin.DELETE("/contact-groups/:id", notice.DeleteContactGroup)
		noticeGRAdmin.GET("/all/contact-groups", notice.GetAllContactGroups)
		noticeGRAdmin.GET("/messages", notice.GetMessages)
		noticeGRAdmin.GET("/messages/:id", notice.GetMessage)
		noticeGRAdmin.POST("/test/dingtalk", notice.DingtalkTestContent)
	}
	noteGRAdmin := apiV1GR.Group("/note", adminRequired())
	{
		noteGRAdmin.GET("/bookmarks", note.GetBookmarks)
		noteGRAdmin.POST("/bookmarks", note.CreateBookmark)
		noteGRAdmin.PUT("/bookmarks/:id", note.UpdateBookmark)
		noteGRAdmin.DELETE("/bookmarks/:id", note.DeleteBookmark)
		// 值班
		noteGRAdmin.GET("/duty/members", note.GetDutyMembers)
		noteGRAdmin.PUT("/duty/members", note.UpdateDutyMembers)
		noteGRAdmin.PUT("/duty/notify", note.NotifyDuty)
		noteGRAdmin.GET("/duty", note.GetDutys)
		noteGRAdmin.PUT("/duty", note.AdjustSchedule)
		noteGRAdmin.POST("/duty/reschedule", note.ReAutoSchedule)
		// 故障记录
		noteGRAdmin.POST("/journals", note.AddJournal)
		noteGRAdmin.PUT("/journals/:id", note.EditJournal)
		noteGRAdmin.DELETE("/journals/:id", note.DeleteJournal)
	}
	noteGRQA := apiV1GR.Group("/note", qaRequired())
	{
		noteGRQA.GET("/journals", note.GetJournals)
		noteGRQA.GET("/journals/:id", note.GetJournal)
	}

	workflowGRAdmin := apiV1GR.Group("/workflow", adminRequired())
	{
		workflowGRAdmin.GET("/flows", workflow.GetAllFlow)
		workflowGRAdmin.PUT("/flow", workflow.UpdateFlow)
		workflowGRAdmin.POST("/order/exec/mysql/sql", workflow.ExecuteSQL)
		workflowGRAdmin.GET("/order/mysql/tables/:sn", workflow.GetOrderMySQLTableMetaData)
		workflowGRAdmin.PUT("/order/gitlab/pipeline/:id", workflow.AddProjectMember)
		workflowGRAdmin.GET("/all/orders", workflow.GetOrders)
	}
	workflowGRAAuthed := apiV1GR.Group("/workflow", loginRequired())
	{
		workflowGRAAuthed.GET("/order/uploads/*filename", upload.DatabaseOrderDownload)
		workflowGRAAuthed.POST("/order/upload", upload.DatabaseOrderUpLoad)
		// 申请者权限可以操作的
		workflowGRAAuthed.GET("/order/processes/:code", workflow.GetOrderProcesses)
		workflowGRAAuthed.POST("/order/apply/:code", workflow.ApplyOrder)
		workflowGRAAuthed.PUT("/order/update/:id", workflow.UpdateOrder)
		workflowGRAAuthed.GET("/my/orders", workflow.GetMyOrders)
		workflowGRAAuthed.GET("/my/all/orders", workflow.GetMyAllOrders)
		workflowGRAAuthed.GET("/order/detail/:id", workflow.GetOrderDetail)
		workflowGRAAuthed.POST("/approve/order/:id", workflow.ApproveOrder)
		workflowGRAAuthed.POST("/cancel/order/:id", workflow.CancelOrder)
		workflowGRAAuthed.GET("/server/models", workflow.GetServerModels)
		workflowGRAAuthed.POST("/orders/:id/:action/process", workflow.AddOrTransferOrderProcess)
		workflowGRAAuthed.POST("/order/check/mysql/sql", workflow.CheckSQL)
		workflowGRAAuthed.GET("/approver/orders", workflow.GetApproverOrders)
		workflowGRAAuthed.POST("/order/evaluate", workflow.EveluateOrder)
		workflowGRAAuthed.POST("/order/comments", workflow.AddOrderComment)
		workflowGRAAuthed.PUT("/order/comments/:id", workflow.UpdateOrderComment)
	}
	// 管理员权限可以操作的
	workflowGRAdminRole := apiV1GR.Group("/workflow/admin", workflowAdminRequired())
	{
		workflowGRAdminRole.GET("/:applicant_id/my/all/orders", workflow.GetAdminMyAllOrders)
		workflowGRAdminRole.POST("/:applicant_id/apply/:code", workflow.AdminApplyOrder)
		workflowGRAdminRole.GET("/:applicant_id/processes/:code", workflow.GetAdminOrderProcesses)
	}
	// 监控
	monitorGRAdmin := apiV1GR.Group("/monitor", adminRequired())
	{
		monitorGRAdmin.GET("/domain-https", monitor.GetDomainHttps)
		monitorGRAdmin.GET("/domain-https/:id/info", monitor.GetDomainHttpsInfo)
		monitorGRAdmin.POST("/domain-https", monitor.CreateDomainHttps)
		monitorGRAdmin.PUT("/domain-https/:id", monitor.UpdateDomainHttps)
		monitorGRAdmin.DELETE("/domain-https/:id", monitor.DeleteDomainHttps)
		monitorGRAdmin.POST("/domain-https/auto-create", monitor.AutoCreateDomainHttps)
		monitorGRAdmin.POST("/domain-https/auto-check", monitor.AutoCheckDomainHttps)
		monitorGRAdmin.POST("/domain-https/bat-check", monitor.BatCheckDomainHttpsIsSecure)
		monitorGRAdmin.POST("/domain-https/bat-switch-notice", monitor.BatSwitchNoticeDomainHttps)
		monitorGRAdmin.POST("/domain-https/bat-delete", monitor.BatDeleteDomainHttps)
		// prometheus
		monitorGRAdmin.GET("/prometheus/instances", monitor.GetPrometheusInstances)
		monitorGRAdmin.POST("/prometheus/instances", monitor.CreatePrometheusInstances)
		monitorGRAdmin.DELETE("/prometheus/instances/:id", monitor.DeletePrometheusInstance)
		monitorGRAdmin.POST("/prometheus/instances/:id/test-connect", monitor.TestConnectPrometheusInstance)
		monitorGRAdmin.PUT("/prometheus/instances/:id", monitor.UpdatePrometheusInstance)
		// 主机指标
		monitorGRAdmin.POST("/prometheus/host/sync", monitor.SyncHostMetrics)
		monitorGRAdmin.GET("/prometheus/host/metrics", monitor.GetHostMetrics)
		// 主机低使用率
		monitorGRAdmin.GET("/prometheus/host/low_usage", monitor.GetLowUsageHosts)
	}
	settingGRAdmin := apiV1GR.Group("/setting", adminRequired())
	{
		settingGRAdmin.PUT("/update", setting.UpdateSetting)
		settingGRAdmin.GET("/sections", setting.GetSettingSections)
		settingGRAdmin.GET("/items/:section", setting.GetSettingSectionItems)
	}

	// key验证的接口
	apiV2GRKey := r.Group("/api/v2", KeyRequired())
	{
		apiV2GRKey.GET("/asset/monitor/hosts", v2.GetMonitorHosts)
		apiV2GRKey.GET("/assets/all/hosts", asset.GetAllHosts)
		apiV2GRKey.GET("/assets/all", asset.GetAllOldHosts)
		apiV2GRKey.GET("/query/pvtz/r", v2.GetPVTZRecordsByValue)
	}
	apiV2BasicAuthRouterGroup := r.Group("/api/v2", gin.BasicAuth(gin.Accounts{
		"nagios": "nagiosAdmin2019",
		"query":  "query@CMDB2022",
	}))
	{
		apiV2BasicAuthRouterGroup.GET("/query/pvtz/rr", v2.GetPVTZRecordsByValue)
	}
	MySQLkeyRouter := r.Group("/api/v1", KeyRequired())
	{
		MySQLkeyRouter.POST("/dbbackups/records", mysql.CreateBackup)
		MySQLkeyRouter.GET("/mysql/user_priv_db_table_list", mysql.GetUserPrivDBTableList)
		MySQLkeyRouter.GET("/mysql/db_table_columns", mysql.GetDBTableColumns)
		MySQLkeyRouter.GET("/mysql/instances_databases", mysql.GetCIInstancesDatabases)
		MySQLkeyRouter.GET("/mysql/instance_database_tables", mysql.GetCIInstanceDatabaseTables)
	}
	return
}
