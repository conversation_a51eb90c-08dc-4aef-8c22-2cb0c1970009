package api

import (
	"cmdb/app"
	"cmdb/app/api/auth"
	"cmdb/app/api/dashboard"
	"cmdb/app/api/robot"
	"net/http"

	v2 "cmdb/app/api/v2"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus/promhttp"
)

// setupBaseRoutes 设置基础路由（不需要认证的路由）
func setupBaseRoutes(r *gin.Engine) {
	// 基础监控和健康检查路由
	prometheusHandler := promhttp.Handler()
	r.GET("/metrics", gin.WrapH(prometheusHandler))
	r.GET("/machineuse", dashboard.MachineUse)
	r.Any("/ping", func(c *gin.Context) { c.String(http.StatusOK, "PONG") })

	// 认证相关路由
	r.POST("/login", auth.Login)
	r.POST("/access_key_login", auth.KeyAuth)
	r.POST("/dingchat/:usage", robot.DingTalkChatAPI)
	r.POST("/api/refresh-token", auth.RefreshToken)
	r.POST("/api/oalogin", auth.OAAuth)
	r.PUT("/api/gateway/login", auth.OAGatewayLogin)
}

// setupAPIV1Routes 设置API v1路由组
func setupAPIV1Routes(r *gin.Engine) *gin.RouterGroup {
	apiV1GR := r.Group("/api/v1")
	apiV1GR.GET("/get-async-routes", func(c *gin.Context) {
		// 获取当前用户有权限的异步路由
		// 暂时返回空
		c.JSON(http.StatusOK, []string{})
	}, loginRequired())

	return apiV1GR
}

// setupAPIV2Routes 设置API v2路由组
func setupAPIV2Routes(r *gin.Engine) {
	// key验证的接口
	apiV2GRKey := r.Group("/api/v2", KeyRequired())
	{
		apiV2GRKey.GET("/asset/monitor/hosts", v2.GetMonitorHosts)
		apiV2GRKey.GET("/assets/all/hosts", asset.GetAllHosts)
		apiV2GRKey.GET("/assets/all", asset.GetAllOldHosts)
		apiV2GRKey.GET("/query/pvtz/r", v2.GetPVTZRecordsByValue)
	}

	// Basic Auth路由组
	apiV2BasicAuthRouterGroup := r.Group("/api/v2", gin.BasicAuth(gin.Accounts{
		"nagios": "nagiosAdmin2019",
		"query":  "query@CMDB2022",
	}))
	{
		apiV2BasicAuthRouterGroup.GET("/query/pvtz/rr", v2.GetPVTZRecordsByValue)
	}
}
