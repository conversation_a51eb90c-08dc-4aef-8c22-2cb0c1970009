package api

import (
	statisticAsset "cmdb/app/api/statistic/asset"
	bill "cmdb/app/api/statistic/bill"
	statisticMonitor "cmdb/app/api/statistic/monitor"

	"github.com/gin-gonic/gin"
)

// setupStatisticRoutes 设置统计相关路由
func setupStatisticRoutes(apiV1GR *gin.RouterGroup) {
	// 统计路由（需要登录）
	statisticGRAuthed := apiV1GR.Group("/statistic", loginRequired())
	{
		// 资产统计
		setupAssetStatisticRoutes(statisticGRAuthed)
		
		// 账单统计
		setupBillStatisticRoutes(statisticGRAuthed)
		
		// 监控统计
		setupMonitorStatisticRoutes(statisticGRAuthed)
	}

	// 兼容旧的statistics路由，重定向到新的statistic包
	statisticsGRAmin := apiV1GR.Group("/statistics")
	{
		statisticsGRAmin.GET("/asset/stat", statisticAsset.GetMonthlyAssetStat)
		statisticsGRAmin.PUT("/asset/update/:id/database", statisticAsset.UpdateDataAsset)
		statisticsGRAmin.POST("/asset/stat/now", statisticAsset.StatAssetNow)
		statisticsGRAmin.GET("/asset/stat/metrics", statisticAsset.GetAssetMetrics)
	}
}

// setupAssetStatisticRoutes 设置资产统计路由
func setupAssetStatisticRoutes(statisticGRAuthed *gin.RouterGroup) {
	assetStatisticGR := statisticGRAuthed.Group("/asset")
	{
		// 资产指标统计
		assetStatisticGR.GET("/metrics", statisticAsset.GetAssetMetrics)
		assetStatisticGR.GET("/metrics/trend", statisticAsset.GetAssetMetricsTrend)
		assetStatisticGR.GET("/metrics/distribution", statisticAsset.GetAssetMetricsDistribution)
		assetStatisticGR.GET("/metrics/comparison", statisticAsset.GetAssetMetricsComparison)
		
		// 资产类型统计
		assetStatisticGR.GET("/types/count", statisticAsset.GetAssetTypesCount)
		assetStatisticGR.GET("/types/trend", statisticAsset.GetAssetTypesTrend)
		
		// 云厂商统计
		assetStatisticGR.GET("/cloud/distribution", statisticAsset.GetCloudDistribution)
		assetStatisticGR.GET("/cloud/cost", statisticAsset.GetCloudCost)
		
		// 地域统计
		assetStatisticGR.GET("/region/distribution", statisticAsset.GetRegionDistribution)
		
		// 业务统计
		assetStatisticGR.GET("/business/distribution", statisticAsset.GetBusinessDistribution)
		
		// 资源组统计
		assetStatisticGR.GET("/resource-group/distribution", statisticAsset.GetResourceGroupDistribution)
	}
}

// setupBillStatisticRoutes 设置账单统计路由
func setupBillStatisticRoutes(statisticGRAuthed *gin.RouterGroup) {
	billStatisticGR := statisticGRAuthed.Group("/bill")
	{
		// 月度账单统计
		billStatisticGR.GET("/monthly/summary", bill.GetMonthlySummary)
		billStatisticGR.GET("/monthly/trend", bill.GetMonthlyTrend)
		billStatisticGR.GET("/monthly/comparison", bill.GetMonthlyComparison)
		
		// 云厂商账单统计
		billStatisticGR.GET("/cloud/summary", bill.GetCloudSummary)
		billStatisticGR.GET("/cloud/trend", bill.GetCloudTrend)
		billStatisticGR.GET("/cloud/comparison", bill.GetCloudComparison)
		
		// 业务账单统计
		billStatisticGR.GET("/business/summary", bill.GetBusinessSummary)
		billStatisticGR.GET("/business/trend", bill.GetBusinessTrend)
		
		// 资源类型账单统计
		billStatisticGR.GET("/resource-type/summary", bill.GetResourceTypeSummary)
		billStatisticGR.GET("/resource-type/trend", bill.GetResourceTypeTrend)
		
		// 地域账单统计
		billStatisticGR.GET("/region/summary", bill.GetRegionSummary)
		billStatisticGR.GET("/region/trend", bill.GetRegionTrend)
		
		// 预算和预测
		billStatisticGR.GET("/budget/status", bill.GetBudgetStatus)
		billStatisticGR.GET("/forecast", bill.GetCostForecast)
	}
}

// setupMonitorStatisticRoutes 设置监控统计路由
func setupMonitorStatisticRoutes(statisticGRAuthed *gin.RouterGroup) {
	monitorStatisticGR := statisticGRAuthed.Group("/monitor")
	{
		// N9e监控统计
		n9eStatisticGR := monitorStatisticGR.Group("/n9e")
		{
			// 告警统计
			n9eStatisticGR.GET("/alerts/summary", statisticMonitor.GetAlertsSummary)
			n9eStatisticGR.GET("/alerts/trend", statisticMonitor.GetAlertsTrend)
			n9eStatisticGR.GET("/alerts/severity", statisticMonitor.GetAlertsBySeverity)
			n9eStatisticGR.GET("/alerts/recovery-rate", statisticMonitor.GetAlertsRecoveryRate)
			n9eStatisticGR.GET("/alerts/duration", statisticMonitor.GetAlertsDuration)
			n9eStatisticGR.GET("/alerts/top-rules", statisticMonitor.GetTopAlertRules)
			n9eStatisticGR.GET("/alerts/current", statisticMonitor.GetCurrentAlerts)
			
			// 集群统计
			n9eStatisticGR.GET("/clusters/alerts", statisticMonitor.GetClusterAlerts)
			n9eStatisticGR.GET("/clusters/health", statisticMonitor.GetClusterHealth)
			
			// 目标统计
			n9eStatisticGR.GET("/targets/alerts", statisticMonitor.GetTargetAlerts)
			n9eStatisticGR.GET("/targets/health", statisticMonitor.GetTargetHealth)
		}
		
		// 其他监控系统统计（预留）
		// prometheusStatisticGR := monitorStatisticGR.Group("/prometheus")
		// grafanaStatisticGR := monitorStatisticGR.Group("/grafana")
	}
}
