package api

import (
	"cmdb/app/api/mysql"
	"cmdb/app/api/task"

	"github.com/gin-gonic/gin"
)

// setupDatabaseRoutes 设置数据库相关路由
func setupDatabaseRoutes(apiV1GR *gin.RouterGroup) {
	// MySQL管理路由（需要管理员权限）
	mysqlGRAdmin := apiV1GR.Group("/mysql", adminRequired())
	{
		// MySQL实例管理
		mysqlGRAdmin.GET("/instances", mysql.GetInstances)
		mysqlGRAdmin.POST("/instances", mysql.CreateInstance)
		mysqlGRAdmin.PUT("/instances/:id", mysql.UpdateInstance)
		mysqlGRAdmin.DELETE("/instances/:id", mysql.DeleteInstance)
		mysqlGRAdmin.GET("/instances/:id", mysql.GetInstance)
		mysqlGRAdmin.GET("/all/instances", mysql.GetAllInstances)

		// MySQL数据库管理
		mysqlGRAdmin.GET("/instances/:id/databases", mysql.GetInstanceDatabases)
		mysqlGRAdmin.POST("/instances/:id/databases", mysql.CreateInstanceDatabase)
		mysqlGRAdmin.PUT("/databases/:db_id", mysql.UpdateInstanceDatabase)
		mysqlGRAdmin.DELETE("/databases/:db_id", mysql.DeleteInstanceDatabase)

		// MySQL用户管理
		mysqlGRAdmin.GET("/instances/:id/users", mysql.GetInstanceUsers)
		mysqlGRAdmin.POST("/instances/:id/users", mysql.CreateInstanceUser)
		mysqlGRAdmin.PUT("/users/:user_id", mysql.UpdateInstanceUser)
		mysqlGRAdmin.DELETE("/users/:user_id", mysql.DeleteInstanceUser)

		// MySQL权限管理
		mysqlGRAdmin.GET("/users/:user_id/privileges", mysql.GetUserPrivileges)
		mysqlGRAdmin.POST("/users/:user_id/privileges", mysql.GrantUserPrivileges)
		mysqlGRAdmin.DELETE("/users/:user_id/privileges/:priv_id", mysql.RevokeUserPrivileges)
		mysqlGRAdmin.GET("/user_priv_db_table_list", mysql.GetUserPrivDBTableList)

		// MySQL备份管理
		mysqlGRAdmin.GET("/instances/:id/backups", mysql.GetInstanceBackups)
		mysqlGRAdmin.POST("/instances/:id/backups", mysql.CreateInstanceBackup)
		mysqlGRAdmin.DELETE("/backups/:backup_id", mysql.DeleteInstanceBackup)
		mysqlGRAdmin.POST("/backups/:backup_id/restore", mysql.RestoreInstanceBackup)

		// MySQL监控
		mysqlGRAdmin.GET("/instances/:id/status", mysql.GetInstanceStatus)
		mysqlGRAdmin.GET("/instances/:id/processlist", mysql.GetInstanceProcessList)
		mysqlGRAdmin.GET("/instances/:id/variables", mysql.GetInstanceVariables)
		mysqlGRAdmin.GET("/instances/:id/slow-queries", mysql.GetInstanceSlowQueries)

		// MySQL查询工具
		mysqlGRAdmin.GET("/db_table_columns", mysql.GetDBTableColumns)
		mysqlGRAdmin.GET("/instances_databases", mysql.GetCIInstancesDatabases)
		mysqlGRAdmin.GET("/instance_database_tables", mysql.GetCIInstanceDatabaseTables)
	}

	// MySQL Key验证路由
	MySQLkeyRouter := apiV1GR.Group("/mysql", KeyRequired())
	{
		MySQLkeyRouter.POST("/dbbackups/records", mysql.CreateBackup)
		MySQLkeyRouter.GET("/user_priv_db_table_list", mysql.GetUserPrivDBTableList)
		MySQLkeyRouter.GET("/db_table_columns", mysql.GetDBTableColumns)
		MySQLkeyRouter.GET("/instances_databases", mysql.GetCIInstancesDatabases)
		MySQLkeyRouter.GET("/instance_database_tables", mysql.GetCIInstanceDatabaseTables)
	}
}

// setupTaskRoutes 设置任务管理路由
func setupTaskRoutes(apiV1GR *gin.RouterGroup) {
	// 任务管理路由（需要管理员权限）
	taskGRAdmin := apiV1GR.Group("/task", adminRequired())
	{
		// 任务基础管理
		taskGRAdmin.GET("/tasks", task.GetTasks)
		taskGRAdmin.POST("/tasks", task.CreateTask)
		taskGRAdmin.PUT("/tasks/:id", task.UpdateTask)
		taskGRAdmin.DELETE("/tasks/:id", task.DeleteTask)
		taskGRAdmin.GET("/tasks/:id", task.GetTask)

		// 任务执行管理
		taskGRAdmin.POST("/tasks/:id/start", task.StartTask)
		taskGRAdmin.POST("/tasks/:id/stop", task.StopTask)
		taskGRAdmin.POST("/tasks/:id/restart", task.RestartTask)
		taskGRAdmin.GET("/tasks/:id/status", task.GetTaskStatus)
		taskGRAdmin.GET("/tasks/:id/logs", task.GetTaskLogs)

		// 任务调度管理
		taskGRAdmin.GET("/schedules", task.GetTaskSchedules)
		taskGRAdmin.POST("/schedules", task.CreateTaskSchedule)
		taskGRAdmin.PUT("/schedules/:id", task.UpdateTaskSchedule)
		taskGRAdmin.DELETE("/schedules/:id", task.DeleteTaskSchedule)
		taskGRAdmin.POST("/schedules/:id/enable", task.EnableTaskSchedule)
		taskGRAdmin.POST("/schedules/:id/disable", task.DisableTaskSchedule)

		// 任务模板管理
		taskGRAdmin.GET("/templates", task.GetTaskTemplates)
		taskGRAdmin.POST("/templates", task.CreateTaskTemplate)
		taskGRAdmin.PUT("/templates/:id", task.UpdateTaskTemplate)
		taskGRAdmin.DELETE("/templates/:id", task.DeleteTaskTemplate)
		taskGRAdmin.GET("/templates/:id", task.GetTaskTemplate)

		// 任务执行历史
		taskGRAdmin.GET("/executions", task.GetTaskExecutions)
		taskGRAdmin.GET("/executions/:id", task.GetTaskExecution)
		taskGRAdmin.GET("/tasks/:id/executions", task.GetTaskExecutionHistory)

		// 任务统计
		taskGRAdmin.GET("/statistics/summary", task.GetTaskStatisticsSummary)
		taskGRAdmin.GET("/statistics/execution-trend", task.GetTaskExecutionTrend)
		taskGRAdmin.GET("/statistics/success-rate", task.GetTaskSuccessRate)
	}

	// 任务查询路由（需要登录）
	taskGRAuthed := apiV1GR.Group("/task", loginRequired())
	{
		taskGRAuthed.GET("/my-tasks", task.GetMyTasks)
		taskGRAuthed.GET("/my-executions", task.GetMyTaskExecutions)
	}
}
