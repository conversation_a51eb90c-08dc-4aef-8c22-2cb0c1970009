package asset

import (
	"context"
	"fmt"

	"cmdb/internal/domain/asset/entity"
	"cmdb/internal/domain/asset/repository"
	"cmdb/internal/domain/asset/service"
)

// HostApplicationService 主机应用服务
type HostApplicationService struct {
	hostRepo        repository.HostRepository
	hostDomainSvc   *service.HostDomainService
	eventPublisher  EventPublisher
	transactionMgr  TransactionManager
}

// NewHostApplicationService 创建主机应用服务
func NewHostApplicationService(
	hostRepo repository.HostRepository,
	hostDomainSvc *service.HostDomainService,
	eventPublisher EventPublisher,
	transactionMgr TransactionManager,
) *HostApplicationService {
	return &HostApplicationService{
		hostRepo:       hostRepo,
		hostDomainSvc:  hostDomainSvc,
		eventPublisher: eventPublisher,
		transactionMgr: transactionMgr,
	}
}

// CreateHost 创建主机
func (s *HostApplicationService) CreateHost(ctx context.Context, cmd CreateHostCommand) (*HostDTO, error) {
	return s.transactionMgr.ExecuteInTransaction(ctx, func(ctx context.Context) (*HostDTO, error) {
		// 使用领域服务创建主机
		req := service.CreateHostRequest{
			Name:           cmd.Name,
			IPAddress:      cmd.IPAddress,
			CloudAccountID: cmd.CloudAccountID,
			DatacenterID:   cmd.DatacenterID,
			BusinessID:     cmd.BusinessID,
			Tags:           convertToTagRequests(cmd.Tags),
		}

		host, err := s.hostDomainSvc.CreateHost(ctx, req)
		if err != nil {
			return nil, fmt.Errorf("创建主机失败: %w", err)
		}

		// 保存到仓储
		if err := s.hostRepo.Save(ctx, host); err != nil {
			return nil, fmt.Errorf("保存主机失败: %w", err)
		}

		// 发布领域事件
		event := HostCreatedEvent{
			HostID:    host.ID(),
			Name:      host.Name(),
			IPAddress: host.IPAddress().Value(),
			CreatedAt: host.CreatedAt(),
		}
		if err := s.eventPublisher.Publish(ctx, event); err != nil {
			// 事件发布失败不应该影响主流程，记录日志即可
			// TODO: 添加日志记录
		}

		return s.convertToDTO(host), nil
	})
}

// UpdateHost 更新主机
func (s *HostApplicationService) UpdateHost(ctx context.Context, hostID entity.HostID, cmd UpdateHostCommand) (*HostDTO, error) {
	return s.transactionMgr.ExecuteInTransaction(ctx, func(ctx context.Context) (*HostDTO, error) {
		// 使用领域服务更新主机
		req := service.UpdateHostRequest{
			Name:         cmd.Name,
			IPAddress:    cmd.IPAddress,
			DatacenterID: cmd.DatacenterID,
			BusinessID:   cmd.BusinessID,
		}

		host, err := s.hostDomainSvc.UpdateHost(ctx, hostID, req)
		if err != nil {
			return nil, fmt.Errorf("更新主机失败: %w", err)
		}

		// 保存到仓储
		if err := s.hostRepo.Save(ctx, host); err != nil {
			return nil, fmt.Errorf("保存主机失败: %w", err)
		}

		// 发布领域事件
		event := HostUpdatedEvent{
			HostID:    host.ID(),
			Name:      host.Name(),
			IPAddress: host.IPAddress().Value(),
			UpdatedAt: host.UpdatedAt(),
		}
		if err := s.eventPublisher.Publish(ctx, event); err != nil {
			// 事件发布失败不应该影响主流程，记录日志即可
			// TODO: 添加日志记录
		}

		return s.convertToDTO(host), nil
	})
}

// GetHost 获取主机
func (s *HostApplicationService) GetHost(ctx context.Context, hostID entity.HostID) (*HostDTO, error) {
	host, err := s.hostRepo.FindByID(ctx, hostID)
	if err != nil {
		return nil, fmt.Errorf("查找主机失败: %w", err)
	}
	if host == nil {
		return nil, fmt.Errorf("主机不存在")
	}

	return s.convertToDTO(host), nil
}

// ListHosts 列出主机
func (s *HostApplicationService) ListHosts(ctx context.Context, query ListHostsQuery) (*HostListDTO, error) {
	// 构建搜索条件
	criteria := repository.HostSearchCriteria{
		Name:           query.Name,
		IPAddress:      query.IPAddress,
		Status:         query.Status,
		CloudAccountID: query.CloudAccountID,
		DatacenterID:   query.DatacenterID,
		BusinessID:     query.BusinessID,
		Offset:         query.Offset,
		Limit:          query.Limit,
		OrderBy:        query.OrderBy,
		OrderDesc:      query.OrderDesc,
		NameLike:       query.NameLike,
		IPAddressLike:  query.IPAddressLike,
		IncludeDeleted: query.IncludeDeleted,
	}

	// 查询主机列表
	hosts, err := s.hostRepo.FindAll(ctx, criteria)
	if err != nil {
		return nil, fmt.Errorf("查询主机列表失败: %w", err)
	}

	// 查询总数
	total, err := s.hostRepo.Count(ctx, criteria)
	if err != nil {
		return nil, fmt.Errorf("查询主机总数失败: %w", err)
	}

	// 转换为DTO
	hostDTOs := make([]*HostDTO, len(hosts))
	for i, host := range hosts {
		hostDTOs[i] = s.convertToDTO(host)
	}

	return &HostListDTO{
		Hosts: hostDTOs,
		Total: total,
	}, nil
}

// DeleteHost 删除主机
func (s *HostApplicationService) DeleteHost(ctx context.Context, hostID entity.HostID) error {
	return s.transactionMgr.ExecuteInTransaction(ctx, func(ctx context.Context) error {
		// 查找主机
		host, err := s.hostRepo.FindByID(ctx, hostID)
		if err != nil {
			return fmt.Errorf("查找主机失败: %w", err)
		}
		if host == nil {
			return fmt.Errorf("主机不存在")
		}

		// 软删除主机
		host.Delete()

		// 保存到仓储
		if err := s.hostRepo.Save(ctx, host); err != nil {
			return fmt.Errorf("删除主机失败: %w", err)
		}

		// 发布领域事件
		event := HostDeletedEvent{
			HostID:    host.ID(),
			Name:      host.Name(),
			DeletedAt: host.UpdatedAt(),
		}
		if err := s.eventPublisher.Publish(ctx, event); err != nil {
			// 事件发布失败不应该影响主流程，记录日志即可
			// TODO: 添加日志记录
		}

		return nil
	})
}

// BatchUpdateHostStatus 批量更新主机状态
func (s *HostApplicationService) BatchUpdateHostStatus(ctx context.Context, cmd BatchUpdateHostStatusCommand) error {
	return s.transactionMgr.ExecuteInTransaction(ctx, func(ctx context.Context) error {
		// 使用领域服务批量更新状态
		if err := s.hostDomainSvc.BatchUpdateHostStatus(ctx, cmd.HostIDs, cmd.Status); err != nil {
			return fmt.Errorf("批量更新主机状态失败: %w", err)
		}

		// 发布领域事件
		event := HostStatusBatchUpdatedEvent{
			HostIDs: cmd.HostIDs,
			Status:  cmd.Status,
		}
		if err := s.eventPublisher.Publish(ctx, event); err != nil {
			// 事件发布失败不应该影响主流程，记录日志即可
			// TODO: 添加日志记录
		}

		return nil
	})
}

// BatchAddHostTags 批量添加主机标签
func (s *HostApplicationService) BatchAddHostTags(ctx context.Context, cmd BatchAddHostTagsCommand) error {
	return s.transactionMgr.ExecuteInTransaction(ctx, func(ctx context.Context) error {
		// 使用领域服务批量添加标签
		tags := convertToTagRequests(cmd.Tags)
		if err := s.hostDomainSvc.BatchAddHostTags(ctx, cmd.HostIDs, tags); err != nil {
			return fmt.Errorf("批量添加主机标签失败: %w", err)
		}

		// 发布领域事件
		event := HostTagsBatchAddedEvent{
			HostIDs: cmd.HostIDs,
			Tags:    cmd.Tags,
		}
		if err := s.eventPublisher.Publish(ctx, event); err != nil {
			// 事件发布失败不应该影响主流程，记录日志即可
			// TODO: 添加日志记录
		}

		return nil
	})
}

// convertToDTO 转换为DTO
func (s *HostApplicationService) convertToDTO(host *entity.Host) *HostDTO {
	dto := &HostDTO{
		ID:        host.ID().Value(),
		Name:      host.Name(),
		IPAddress: host.IPAddress().Value(),
		Status:    host.Status().String(),
		CreatedAt: host.CreatedAt(),
		UpdatedAt: host.UpdatedAt(),
		Version:   host.Version(),
	}

	// 转换云账户信息
	if cloudAccount := host.CloudAccount(); cloudAccount != nil {
		dto.CloudAccount = &CloudAccountDTO{
			ID:       cloudAccount.ID().Value(),
			Name:     cloudAccount.Name(),
			Provider: cloudAccount.Provider().String(),
		}
	}

	// 转换数据中心信息
	if datacenter := host.Datacenter(); datacenter != nil {
		dto.Datacenter = &DatacenterDTO{
			ID:       datacenter.ID().Value(),
			Name:     datacenter.Name(),
			Location: datacenter.Location(),
		}
	}

	// 转换业务信息
	if business := host.Business(); business != nil {
		dto.Business = &BusinessDTO{
			ID:   business.ID().Value(),
			Name: business.Name(),
			Code: business.Code(),
		}
	}

	// 转换标签信息
	tags := host.Tags()
	dto.Tags = make([]TagDTO, len(tags))
	for i, tag := range tags {
		dto.Tags[i] = TagDTO{
			Key:   tag.Key(),
			Value: tag.Value(),
		}
	}

	return dto
}

// convertToTagRequests 转换为标签请求
func convertToTagRequests(tags []TagDTO) []service.TagRequest {
	requests := make([]service.TagRequest, len(tags))
	for i, tag := range tags {
		requests[i] = service.TagRequest{
			Key:   tag.Key,
			Value: tag.Value,
		}
	}
	return requests
}
