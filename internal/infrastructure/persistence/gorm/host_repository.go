package gorm

import (
	"context"
	"errors"
	"fmt"

	"cmdb/internal/domain/asset/entity"
	"cmdb/internal/domain/asset/repository"
	"cmdb/internal/domain/asset/valueobject"

	"gorm.io/gorm"
)

// HostRepositoryImpl 主机仓储GORM实现
type HostRepositoryImpl struct {
	db *gorm.DB
}

// NewHostRepository 创建主机仓储实现
func NewHostRepository(db *gorm.DB) repository.HostRepository {
	return &HostRepositoryImpl{db: db}
}

// HostPO 主机持久化对象
type HostPO struct {
	ID             uint64 `gorm:"column:id;primaryKey;autoIncrement"`
	Name           string `gorm:"column:name;type:varchar(255);not null;uniqueIndex"`
	IPAddress      string `gorm:"column:ip_address;type:varchar(45);not null;uniqueIndex"`
	CloudAccountID *uint64 `gorm:"column:cloud_account_id;index"`
	DatacenterID   *uint64 `gorm:"column:datacenter_id;index"`
	BusinessID     *uint64 `gorm:"column:business_id;index"`
	Status         int8    `gorm:"column:status;not null;default:1"`
	CreatedAt      int64   `gorm:"column:created_at;not null"`
	UpdatedAt      int64   `gorm:"column:updated_at;not null"`
	Version        int     `gorm:"column:version;not null;default:1"`

	// 关联对象
	CloudAccount *CloudAccountPO `gorm:"foreignKey:CloudAccountID"`
	Datacenter   *DatacenterPO   `gorm:"foreignKey:DatacenterID"`
	Business     *BusinessPO     `gorm:"foreignKey:BusinessID"`
	Tags         []HostTagPO     `gorm:"foreignKey:HostID"`
}

// TableName 表名
func (HostPO) TableName() string {
	return "hosts"
}

// HostTagPO 主机标签持久化对象
type HostTagPO struct {
	ID     uint64 `gorm:"column:id;primaryKey;autoIncrement"`
	HostID uint64 `gorm:"column:host_id;not null;index"`
	Key    string `gorm:"column:tag_key;type:varchar(128);not null"`
	Value  string `gorm:"column:tag_value;type:varchar(256);not null"`
}

// TableName 表名
func (HostTagPO) TableName() string {
	return "host_tags"
}

// CloudAccountPO 云账户持久化对象
type CloudAccountPO struct {
	ID          uint64 `gorm:"column:id;primaryKey;autoIncrement"`
	Name        string `gorm:"column:name;type:varchar(255);not null"`
	Provider    string `gorm:"column:provider;type:varchar(32);not null"`
	AccessKey   string `gorm:"column:access_key;type:varchar(255);not null"`
	SecretKey   string `gorm:"column:secret_key;type:varchar(255);not null"`
	Region      string `gorm:"column:region;type:varchar(64)"`
	Description string `gorm:"column:description;type:text"`
	Status      int8   `gorm:"column:status;not null;default:0"`
	CreatedAt   int64  `gorm:"column:created_at;not null"`
	UpdatedAt   int64  `gorm:"column:updated_at;not null"`
	Version     int    `gorm:"column:version;not null;default:1"`
}

// TableName 表名
func (CloudAccountPO) TableName() string {
	return "cloud_accounts"
}

// DatacenterPO 数据中心持久化对象
type DatacenterPO struct {
	ID          uint64 `gorm:"column:id;primaryKey;autoIncrement"`
	Name        string `gorm:"column:name;type:varchar(255);not null"`
	Location    string `gorm:"column:location;type:varchar(255)"`
	Description string `gorm:"column:description;type:text"`
	CreatedAt   int64  `gorm:"column:created_at;not null"`
	UpdatedAt   int64  `gorm:"column:updated_at;not null"`
	Version     int    `gorm:"column:version;not null;default:1"`
}

// TableName 表名
func (DatacenterPO) TableName() string {
	return "datacenters"
}

// BusinessPO 业务持久化对象
type BusinessPO struct {
	ID          uint64 `gorm:"column:id;primaryKey;autoIncrement"`
	Name        string `gorm:"column:name;type:varchar(255);not null"`
	Code        string `gorm:"column:code;type:varchar(64);not null;uniqueIndex"`
	Description string `gorm:"column:description;type:text"`
	Owner       string `gorm:"column:owner;type:varchar(255)"`
	Contact     string `gorm:"column:contact;type:varchar(255)"`
	CreatedAt   int64  `gorm:"column:created_at;not null"`
	UpdatedAt   int64  `gorm:"column:updated_at;not null"`
	Version     int    `gorm:"column:version;not null;default:1"`
}

// TableName 表名
func (BusinessPO) TableName() string {
	return "businesses"
}

// Save 保存主机
func (r *HostRepositoryImpl) Save(ctx context.Context, host *entity.Host) error {
	po := r.entityToPO(host)

	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if po.ID == 0 {
			// 新增
			if err := tx.Create(po).Error; err != nil {
				return fmt.Errorf("创建主机失败: %w", err)
			}
			// 设置生成的ID
			host.SetID(entity.NewHostID(po.ID))
		} else {
			// 更新
			if err := tx.Save(po).Error; err != nil {
				return fmt.Errorf("更新主机失败: %w", err)
			}
		}

		// 更新标签
		if err := r.saveTags(tx, po.ID, host.Tags()); err != nil {
			return fmt.Errorf("保存主机标签失败: %w", err)
		}

		return nil
	})
}

// FindByID 根据ID查找主机
func (r *HostRepositoryImpl) FindByID(ctx context.Context, id entity.HostID) (*entity.Host, error) {
	var po HostPO
	err := r.db.WithContext(ctx).
		Preload("CloudAccount").
		Preload("Datacenter").
		Preload("Business").
		Preload("Tags").
		First(&po, id.Value()).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("查找主机失败: %w", err)
	}

	return r.poToEntity(&po)
}

// FindByName 根据名称查找主机
func (r *HostRepositoryImpl) FindByName(ctx context.Context, name string) (*entity.Host, error) {
	var po HostPO
	err := r.db.WithContext(ctx).
		Preload("CloudAccount").
		Preload("Datacenter").
		Preload("Business").
		Preload("Tags").
		Where("name = ?", name).
		First(&po).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("查找主机失败: %w", err)
	}

	return r.poToEntity(&po)
}

// FindByIPAddress 根据IP地址查找主机
func (r *HostRepositoryImpl) FindByIPAddress(ctx context.Context, ip valueobject.IPAddress) (*entity.Host, error) {
	var po HostPO
	err := r.db.WithContext(ctx).
		Preload("CloudAccount").
		Preload("Datacenter").
		Preload("Business").
		Preload("Tags").
		Where("ip_address = ?", ip.Value()).
		First(&po).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("查找主机失败: %w", err)
	}

	return r.poToEntity(&po)
}

// FindAll 查找所有主机
func (r *HostRepositoryImpl) FindAll(ctx context.Context, criteria repository.HostSearchCriteria) ([]*entity.Host, error) {
	query := r.db.WithContext(ctx).
		Preload("CloudAccount").
		Preload("Datacenter").
		Preload("Business").
		Preload("Tags")

	// 应用搜索条件
	query = r.applyCriteria(query, criteria)

	// 应用分页
	if criteria.Offset > 0 {
		query = query.Offset(criteria.Offset)
	}
	if criteria.Limit > 0 {
		query = query.Limit(criteria.Limit)
	}

	// 应用排序
	if criteria.OrderBy != "" {
		orderClause := criteria.OrderBy
		if criteria.OrderDesc {
			orderClause += " DESC"
		}
		query = query.Order(orderClause)
	}

	var pos []HostPO
	if err := query.Find(&pos).Error; err != nil {
		return nil, fmt.Errorf("查找主机列表失败: %w", err)
	}

	hosts := make([]*entity.Host, len(pos))
	for i, po := range pos {
		host, err := r.poToEntity(&po)
		if err != nil {
			return nil, fmt.Errorf("转换主机实体失败: %w", err)
		}
		hosts[i] = host
	}

	return hosts, nil
}

// Count 统计主机数量
func (r *HostRepositoryImpl) Count(ctx context.Context, criteria repository.HostSearchCriteria) (int64, error) {
	query := r.db.WithContext(ctx).Model(&HostPO{})
	query = r.applyCriteria(query, criteria)

	var count int64
	if err := query.Count(&count).Error; err != nil {
		return 0, fmt.Errorf("统计主机数量失败: %w", err)
	}

	return count, nil
}

// Delete 删除主机
func (r *HostRepositoryImpl) Delete(ctx context.Context, id entity.HostID) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 删除标签
		if err := tx.Where("host_id = ?", id.Value()).Delete(&HostTagPO{}).Error; err != nil {
			return fmt.Errorf("删除主机标签失败: %w", err)
		}

		// 删除主机
		if err := tx.Delete(&HostPO{}, id.Value()).Error; err != nil {
			return fmt.Errorf("删除主机失败: %w", err)
		}

		return nil
	})
}

// ExistsByName 检查名称是否存在
func (r *HostRepositoryImpl) ExistsByName(ctx context.Context, name string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&HostPO{}).Where("name = ?", name).Count(&count).Error
	if err != nil {
		return false, fmt.Errorf("检查主机名称是否存在失败: %w", err)
	}
	return count > 0, nil
}

// ExistsByIPAddress 检查IP地址是否存在
func (r *HostRepositoryImpl) ExistsByIPAddress(ctx context.Context, ip valueobject.IPAddress) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&HostPO{}).Where("ip_address = ?", ip.Value()).Count(&count).Error
	if err != nil {
		return false, fmt.Errorf("检查IP地址是否存在失败: %w", err)
	}
	return count > 0, nil
}
