package asset

import (
	"errors"
	"strconv"

	"cmdb/internal/application/asset"
	"cmdb/internal/domain/asset/entity"
	"cmdb/pkg/response"

	"github.com/gin-gonic/gin"
)

// HostHandler 主机HTTP处理器
type HostHandler struct {
	hostAppSvc *asset.HostApplicationService
}

// NewHostHandler 创建主机HTTP处理器
func NewHostHandler(hostAppSvc *asset.HostApplicationService) *HostHandler {
	return &HostHandler{
		hostAppSvc: hostAppSvc,
	}
}

// CreateHost 创建主机
// @Summary 创建主机
// @Description 创建新的主机资源
// @Tags 主机管理
// @Accept json
// @Produce json
// @Param request body asset.CreateHostCommand true "创建主机请求"
// @Success 201 {object} response.Response{data=asset.HostDTO} "创建成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "内部服务器错误"
// @Router /api/v1/asset/hosts [post]
func (h *HostHandler) CreateHost(c *gin.Context) {
	var cmd asset.CreateHostCommand
	if err := c.ShouldBindJSON(&cmd); err != nil {
		response.FailWithMessage(c, "请求参数错误: "+err.Error())
		return
	}

	// 验证请求参数
	if err := h.validateCreateHostCommand(cmd); err != nil {
		response.FailWithMessage(c, err.Error())
		return
	}

	// 调用应用服务
	hostDTO, err := h.hostAppSvc.CreateHost(c.Request.Context(), cmd)
	if err != nil {
		response.FailWithMessage(c, "创建主机失败: "+err.Error())
		return
	}

	response.OkWithData(c, hostDTO)
}

// GetHost 获取主机详情
// @Summary 获取主机详情
// @Description 根据ID获取主机详细信息
// @Tags 主机管理
// @Accept json
// @Produce json
// @Param id path int true "主机ID"
// @Success 200 {object} response.Response{data=asset.HostDTO} "获取成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "主机不存在"
// @Failure 500 {object} response.Response "内部服务器错误"
// @Router /api/v1/asset/hosts/{id} [get]
func (h *HostHandler) GetHost(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.FailWithMessage(c, "无效的主机ID")
		return
	}

	hostID := entity.NewHostID(id)
	hostDTO, err := h.hostAppSvc.GetHost(c.Request.Context(), hostID)
	if err != nil {
		if err.Error() == "主机不存在" {
			response.FailWithMessage(c, "主机不存在")
			return
		}
		response.FailWithMessage(c, "获取主机失败: "+err.Error())
		return
	}

	response.OkWithData(c, hostDTO)
}

// ListHosts 列出主机
// @Summary 列出主机
// @Description 根据条件查询主机列表
// @Tags 主机管理
// @Accept json
// @Produce json
// @Param name query string false "主机名称"
// @Param ip_address query string false "IP地址"
// @Param status query int false "主机状态"
// @Param cloud_account_id query int false "云账户ID"
// @Param datacenter_id query int false "数据中心ID"
// @Param business_id query int false "业务ID"
// @Param name_like query string false "主机名称模糊搜索"
// @Param ip_address_like query string false "IP地址模糊搜索"
// @Param include_deleted query bool false "是否包含已删除的主机"
// @Param offset query int false "偏移量" default(0)
// @Param limit query int false "限制数量" default(20)
// @Param order_by query string false "排序字段" default("created_at")
// @Param order_desc query bool false "是否降序" default(true)
// @Success 200 {object} response.Response{data=asset.HostListDTO} "查询成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "内部服务器错误"
// @Router /api/v1/asset/hosts [get]
func (h *HostHandler) ListHosts(c *gin.Context) {
	query := asset.ListHostsQuery{
		Name:           c.Query("name"),
		IPAddress:      c.Query("ip_address"),
		NameLike:       c.Query("name_like"),
		IPAddressLike:  c.Query("ip_address_like"),
		IncludeDeleted: c.Query("include_deleted") == "true",
		OrderBy:        c.DefaultQuery("order_by", "created_at"),
		OrderDesc:      c.DefaultQuery("order_desc", "true") == "true",
	}

	// 解析可选的整数参数
	if statusStr := c.Query("status"); statusStr != "" {
		if status, err := strconv.Atoi(statusStr); err == nil {
			hostStatus := entity.HostStatus(status)
			query.Status = &hostStatus
		}
	}

	if cloudAccountIDStr := c.Query("cloud_account_id"); cloudAccountIDStr != "" {
		if id, err := strconv.ParseUint(cloudAccountIDStr, 10, 64); err == nil {
			cloudAccountID := entity.NewCloudAccountID(id)
			query.CloudAccountID = &cloudAccountID
		}
	}

	if datacenterIDStr := c.Query("datacenter_id"); datacenterIDStr != "" {
		if id, err := strconv.ParseUint(datacenterIDStr, 10, 64); err == nil {
			datacenterID := entity.NewDatacenterID(id)
			query.DatacenterID = &datacenterID
		}
	}

	if businessIDStr := c.Query("business_id"); businessIDStr != "" {
		if id, err := strconv.ParseUint(businessIDStr, 10, 64); err == nil {
			businessID := entity.NewBusinessID(id)
			query.BusinessID = &businessID
		}
	}

	// 解析分页参数
	if offsetStr := c.Query("offset"); offsetStr != "" {
		if offset, err := strconv.Atoi(offsetStr); err == nil && offset >= 0 {
			query.Offset = offset
		}
	}

	if limitStr := c.Query("limit"); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil && limit > 0 && limit <= 1000 {
			query.Limit = limit
		} else {
			query.Limit = 20 // 默认值
		}
	} else {
		query.Limit = 20 // 默认值
	}

	// 调用应用服务
	result, err := h.hostAppSvc.ListHosts(c.Request.Context(), query)
	if err != nil {
		response.FailWithMessage(c, "查询主机列表失败: "+err.Error())
		return
	}

	response.OkWithData(c, result)
}

// UpdateHost 更新主机
// @Summary 更新主机
// @Description 更新主机信息
// @Tags 主机管理
// @Accept json
// @Produce json
// @Param id path int true "主机ID"
// @Param request body asset.UpdateHostCommand true "更新主机请求"
// @Success 200 {object} response.Response{data=asset.HostDTO} "更新成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "主机不存在"
// @Failure 500 {object} response.Response "内部服务器错误"
// @Router /api/v1/asset/hosts/{id} [put]
func (h *HostHandler) UpdateHost(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.FailWithMessage(c, "无效的主机ID")
		return
	}

	var cmd asset.UpdateHostCommand
	if err := c.ShouldBindJSON(&cmd); err != nil {
		response.FailWithMessage(c, "请求参数错误: "+err.Error())
		return
	}

	hostID := entity.NewHostID(id)
	hostDTO, err := h.hostAppSvc.UpdateHost(c.Request.Context(), hostID, cmd)
	if err != nil {
		if err.Error() == "主机不存在" {
			response.FailWithMessage(c, "主机不存在")
			return
		}
		response.FailWithMessage(c, "更新主机失败: "+err.Error())
		return
	}

	response.OkWithData(c, hostDTO)
}

// DeleteHost 删除主机
// @Summary 删除主机
// @Description 软删除主机
// @Tags 主机管理
// @Accept json
// @Produce json
// @Param id path int true "主机ID"
// @Success 200 {object} response.Response "删除成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 404 {object} response.Response "主机不存在"
// @Failure 500 {object} response.Response "内部服务器错误"
// @Router /api/v1/asset/hosts/{id} [delete]
func (h *HostHandler) DeleteHost(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.FailWithMessage(c, "无效的主机ID")
		return
	}

	hostID := entity.NewHostID(id)
	if err := h.hostAppSvc.DeleteHost(c.Request.Context(), hostID); err != nil {
		if err.Error() == "主机不存在" {
			response.FailWithMessage(c, "主机不存在")
			return
		}
		response.FailWithMessage(c, "删除主机失败: "+err.Error())
		return
	}

	response.Ok(c)
}

// BatchUpdateHostStatus 批量更新主机状态
// @Summary 批量更新主机状态
// @Description 批量更新多个主机的状态
// @Tags 主机管理
// @Accept json
// @Produce json
// @Param request body asset.BatchUpdateHostStatusCommand true "批量更新主机状态请求"
// @Success 200 {object} response.Response "更新成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "内部服务器错误"
// @Router /api/v1/asset/hosts/batch/status [put]
func (h *HostHandler) BatchUpdateHostStatus(c *gin.Context) {
	var cmd asset.BatchUpdateHostStatusCommand
	if err := c.ShouldBindJSON(&cmd); err != nil {
		response.FailWithMessage(c, "请求参数错误: "+err.Error())
		return
	}

	if len(cmd.HostIDs) == 0 {
		response.FailWithMessage(c, "主机ID列表不能为空")
		return
	}

	if err := h.hostAppSvc.BatchUpdateHostStatus(c.Request.Context(), cmd); err != nil {
		response.FailWithMessage(c, "批量更新主机状态失败: "+err.Error())
		return
	}

	response.Ok(c)
}

// BatchAddHostTags 批量添加主机标签
// @Summary 批量添加主机标签
// @Description 批量为多个主机添加标签
// @Tags 主机管理
// @Accept json
// @Produce json
// @Param request body asset.BatchAddHostTagsCommand true "批量添加主机标签请求"
// @Success 200 {object} response.Response "添加成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 500 {object} response.Response "内部服务器错误"
// @Router /api/v1/asset/hosts/batch/tags [post]
func (h *HostHandler) BatchAddHostTags(c *gin.Context) {
	var cmd asset.BatchAddHostTagsCommand
	if err := c.ShouldBindJSON(&cmd); err != nil {
		response.FailWithMessage(c, "请求参数错误: "+err.Error())
		return
	}

	if len(cmd.HostIDs) == 0 {
		response.FailWithMessage(c, "主机ID列表不能为空")
		return
	}

	if len(cmd.Tags) == 0 {
		response.FailWithMessage(c, "标签列表不能为空")
		return
	}

	if err := h.hostAppSvc.BatchAddHostTags(c.Request.Context(), cmd); err != nil {
		response.FailWithMessage(c, "批量添加主机标签失败: "+err.Error())
		return
	}

	response.Ok(c)
}

// validateCreateHostCommand 验证创建主机命令
func (h *HostHandler) validateCreateHostCommand(cmd asset.CreateHostCommand) error {
	if cmd.Name == "" {
		return errors.New("主机名称不能为空")
	}
	if cmd.IPAddress == "" {
		return errors.New("IP地址不能为空")
	}
	return nil
}
