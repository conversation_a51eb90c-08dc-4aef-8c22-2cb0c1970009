package entity

import (
	"errors"
	"fmt"
	"net"
	"strings"
	"time"

	"cmdb/internal/domain/asset/valueobject"
)

// Host 主机聚合根
type Host struct {
	id           HostID
	name         string
	ipAddress    valueobject.IPAddress
	cloudAccount *CloudAccount
	datacenter   *Datacenter
	business     *Business
	tags         []valueobject.Tag
	status       HostStatus
	createdAt    time.Time
	updatedAt    time.Time
	version      int // 乐观锁版本号
}

// HostID 主机唯一标识
type HostID struct {
	value uint64
}

// NewHostID 创建主机ID
func NewHostID(value uint64) HostID {
	return HostID{value: value}
}

// Value 获取ID值
func (h HostID) Value() uint64 {
	return h.value
}

// String 字符串表示
func (h HostID) String() string {
	return fmt.Sprintf("%d", h.value)
}

// HostStatus 主机状态
type HostStatus int

const (
	HostStatusUnknown HostStatus = iota
	HostStatusRunning
	HostStatusStopped
	HostStatusMaintenance
	HostStatusDeleted
)

// String 返回状态字符串
func (s HostStatus) String() string {
	switch s {
	case HostStatusRunning:
		return "running"
	case HostStatusStopped:
		return "stopped"
	case HostStatusMaintenance:
		return "maintenance"
	case HostStatusDeleted:
		return "deleted"
	default:
		return "unknown"
	}
}

// NewHost 创建新主机
func NewHost(name string, ipAddress string, cloudAccount *CloudAccount) (*Host, error) {
	if strings.TrimSpace(name) == "" {
		return nil, errors.New("主机名称不能为空")
	}

	ip, err := valueobject.NewIPAddress(ipAddress)
	if err != nil {
		return nil, fmt.Errorf("无效的IP地址: %w", err)
	}

	now := time.Now()
	return &Host{
		id:           HostID{}, // ID将在持久化时分配
		name:         strings.TrimSpace(name),
		ipAddress:    ip,
		cloudAccount: cloudAccount,
		status:       HostStatusRunning,
		createdAt:    now,
		updatedAt:    now,
		version:      1,
		tags:         make([]valueobject.Tag, 0),
	}, nil
}

// ID 获取主机ID
func (h *Host) ID() HostID {
	return h.id
}

// Name 获取主机名称
func (h *Host) Name() string {
	return h.name
}

// IPAddress 获取IP地址
func (h *Host) IPAddress() valueobject.IPAddress {
	return h.ipAddress
}

// CloudAccount 获取云账户
func (h *Host) CloudAccount() *CloudAccount {
	return h.cloudAccount
}

// Datacenter 获取数据中心
func (h *Host) Datacenter() *Datacenter {
	return h.datacenter
}

// Business 获取业务
func (h *Host) Business() *Business {
	return h.business
}

// Status 获取状态
func (h *Host) Status() HostStatus {
	return h.status
}

// Tags 获取标签
func (h *Host) Tags() []valueobject.Tag {
	return h.tags
}

// CreatedAt 获取创建时间
func (h *Host) CreatedAt() time.Time {
	return h.createdAt
}

// UpdatedAt 获取更新时间
func (h *Host) UpdatedAt() time.Time {
	return h.updatedAt
}

// Version 获取版本号
func (h *Host) Version() int {
	return h.version
}

// UpdateName 更新主机名称
func (h *Host) UpdateName(name string) error {
	if strings.TrimSpace(name) == "" {
		return errors.New("主机名称不能为空")
	}
	h.name = strings.TrimSpace(name)
	h.updatedAt = time.Now()
	h.version++
	return nil
}

// UpdateIPAddress 更新IP地址
func (h *Host) UpdateIPAddress(ipAddress string) error {
	ip, err := valueobject.NewIPAddress(ipAddress)
	if err != nil {
		return fmt.Errorf("无效的IP地址: %w", err)
	}
	h.ipAddress = ip
	h.updatedAt = time.Now()
	h.version++
	return nil
}

// SetDatacenter 设置数据中心
func (h *Host) SetDatacenter(datacenter *Datacenter) {
	h.datacenter = datacenter
	h.updatedAt = time.Now()
	h.version++
}

// SetBusiness 设置业务
func (h *Host) SetBusiness(business *Business) {
	h.business = business
	h.updatedAt = time.Now()
	h.version++
}

// AddTag 添加标签
func (h *Host) AddTag(tag valueobject.Tag) error {
	// 检查标签是否已存在
	for _, existingTag := range h.tags {
		if existingTag.Key() == tag.Key() {
			return fmt.Errorf("标签键 %s 已存在", tag.Key())
		}
	}
	h.tags = append(h.tags, tag)
	h.updatedAt = time.Now()
	h.version++
	return nil
}

// RemoveTag 移除标签
func (h *Host) RemoveTag(key string) {
	for i, tag := range h.tags {
		if tag.Key() == key {
			h.tags = append(h.tags[:i], h.tags[i+1:]...)
			h.updatedAt = time.Now()
			h.version++
			break
		}
	}
}

// UpdateTag 更新标签
func (h *Host) UpdateTag(key, value string) error {
	for i, tag := range h.tags {
		if tag.Key() == key {
			newTag, err := valueobject.NewTag(key, value)
			if err != nil {
				return err
			}
			h.tags[i] = newTag
			h.updatedAt = time.Now()
			h.version++
			return nil
		}
	}
	return fmt.Errorf("标签键 %s 不存在", key)
}

// ChangeStatus 更改状态
func (h *Host) ChangeStatus(status HostStatus) {
	if h.status != status {
		h.status = status
		h.updatedAt = time.Now()
		h.version++
	}
}

// Start 启动主机
func (h *Host) Start() error {
	if h.status == HostStatusDeleted {
		return errors.New("已删除的主机无法启动")
	}
	h.ChangeStatus(HostStatusRunning)
	return nil
}

// Stop 停止主机
func (h *Host) Stop() error {
	if h.status == HostStatusDeleted {
		return errors.New("已删除的主机无法停止")
	}
	h.ChangeStatus(HostStatusStopped)
	return nil
}

// SetMaintenance 设置维护状态
func (h *Host) SetMaintenance() error {
	if h.status == HostStatusDeleted {
		return errors.New("已删除的主机无法设置维护状态")
	}
	h.ChangeStatus(HostStatusMaintenance)
	return nil
}

// Delete 删除主机（软删除）
func (h *Host) Delete() {
	h.ChangeStatus(HostStatusDeleted)
}

// IsRunning 是否运行中
func (h *Host) IsRunning() bool {
	return h.status == HostStatusRunning
}

// IsDeleted 是否已删除
func (h *Host) IsDeleted() bool {
	return h.status == HostStatusDeleted
}

// ValidateForUpdate 验证更新操作
func (h *Host) ValidateForUpdate() error {
	if h.IsDeleted() {
		return errors.New("已删除的主机无法更新")
	}
	return nil
}

// GetTagValue 获取指定标签的值
func (h *Host) GetTagValue(key string) (string, bool) {
	for _, tag := range h.tags {
		if tag.Key() == key {
			return tag.Value(), true
		}
	}
	return "", false
}

// HasTag 检查是否有指定标签
func (h *Host) HasTag(key string) bool {
	_, exists := h.GetTagValue(key)
	return exists
}

// IsInSameNetwork 检查是否在同一网络
func (h *Host) IsInSameNetwork(other *Host, mask net.IPMask) bool {
	return h.ipAddress.IsInSameNetwork(other.ipAddress, mask)
}

// SetID 设置ID（仅供仓储层使用）
func (h *Host) SetID(id HostID) {
	h.id = id
}

// SetVersion 设置版本号（仅供仓储层使用）
func (h *Host) SetVersion(version int) {
	h.version = version
}

// SetCreatedAt 设置创建时间（仅供仓储层使用）
func (h *Host) SetCreatedAt(createdAt time.Time) {
	h.createdAt = createdAt
}

// SetUpdatedAt 设置更新时间（仅供仓储层使用）
func (h *Host) SetUpdatedAt(updatedAt time.Time) {
	h.updatedAt = updatedAt
}
